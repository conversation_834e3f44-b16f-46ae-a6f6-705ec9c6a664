/**
 * 语音录制状态管理器
 * 负责管理录制过程中的所有状态
 */

export interface VoiceRecordingState {
    // 录制相关状态
    isRecording: boolean;
    isPaused: boolean;
    duration: number;
    progress: number;

    // 识别结果状态
    recognitionResults: any[];
    activeSessionId: string | null;

    // 业务相关状态
    taskId: string;
    businessId: string;
    enableMedicalRecordTranscription: boolean;
    chineseExamination: number;
    physicalExamination: number;
    medicalRecordType: number;

    // UI状态
    isMinimized: boolean;
    showRecord: boolean;
    showMedicalRecordView: boolean;

    // 波形数据
    waveformData: number[];

    // 弹窗状态
    toastMessage: string | null;
    toastVisible: boolean;
    dialogVisible: boolean;
    dialogTitle: string | null;
    dialogContent: string | null;
    dialogConfirmText: string | null;
    dialogCancelText: string | null;
    dialogOnConfirm: (() => void) | null;
    dialogOnCancel: (() => void) | null;

    // 动画相关状态
    fullscreenOpacity: number;
    minimizedOpacity: number;
}

export class StateManager {
    private state: VoiceRecordingState;
    private overlayRef: any = null;
    private minimizedOverlayRef: any = null;

    constructor() {
        this.state = this.getInitialState();
    }

    private getInitialState(): VoiceRecordingState {
        return {
            // 录制相关状态
            isRecording: false,
            isPaused: false,
            duration: 0,
            progress: 0,

            // 识别结果状态
            recognitionResults: [],
            activeSessionId: null,

            // 业务相关状态
            taskId: "",
            businessId: "",
            enableMedicalRecordTranscription: false,
            chineseExamination: 0,
            physicalExamination: 0,
            medicalRecordType: 0,

            // UI状态
            isMinimized: false,
            showRecord: false,
            showMedicalRecordView: false,

            // 波形数据
            waveformData: [],

            // 弹窗状态
            toastMessage: null,
            toastVisible: false,
            dialogVisible: false,
            dialogTitle: null,
            dialogContent: null,
            dialogConfirmText: null,
            dialogCancelText: null,
            dialogOnConfirm: null,
            dialogOnCancel: null,

            // 动画相关状态
            fullscreenOpacity: 0,
            minimizedOpacity: 0,
        };
    }

    public getState(): VoiceRecordingState {
        return { ...this.state };
    }

    public setState(newState: Partial<VoiceRecordingState>, callback?: () => void): void {
        this.state = { ...this.state, ...newState };

        // 同步状态到 View 组件
        if (this.overlayRef && this.overlayRef.setState) {
            this.overlayRef.setState(newState, callback);
        } else if (callback) {
            callback();
        }

        // 同步状态到最小化浮窗组件
        if (this.minimizedOverlayRef && this.minimizedOverlayRef.setState) {
            this.minimizedOverlayRef.setState({
                forceUpdate: Date.now(),
            });
        }

        // 如果是时间更新，额外打印日志
        if (newState.duration !== undefined) {
            console.log("StateManager", "Duration updated to:", newState.duration, "minimizedOverlayRef exists:", !!this.minimizedOverlayRef);
        }
    }

    public updateState(updater: (prevState: VoiceRecordingState) => Partial<VoiceRecordingState>, callback?: () => void): void {
        const newState = updater(this.state);
        this.setState(newState, callback);
    }

    public resetState(): void {
        this.state = this.getInitialState();
    }

    public setOverlayRef(ref: any): void {
        this.overlayRef = ref;
    }

    public setMinimizedOverlayRef(ref: any): void {
        this.minimizedOverlayRef = ref;
    }

    public getOverlayRef(): any {
        return this.overlayRef;
    }

    public getMinimizedOverlayRef(): any {
        return this.minimizedOverlayRef;
    }
}
