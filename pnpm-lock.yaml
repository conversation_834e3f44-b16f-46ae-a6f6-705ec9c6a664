lockfileVersion: 5.4

importers:

  .:
    specifiers:
      '@app/abc-mobile-ui': workspace:*
      '@app/theme': workspace:*
      '@babel/core': ^7.7.5
      '@babel/plugin-proposal-class-properties': ^7.7.4
      '@babel/plugin-proposal-decorators': ^7.8.3
      '@babel/plugin-proposal-nullish-coalescing-operator': ^7.8.3
      '@babel/plugin-proposal-object-rest-spread': ^7.5.5
      '@babel/plugin-proposal-optional-chaining': ^7.9.0
      '@babel/polyfill': ^7.12.0
      '@babel/preset-env': ^7.7.6
      '@babel/preset-react': ^7.7.4
      '@babel/preset-typescript': ^7.8.3
      '@hippy/debug-server-next': latest
      '@hippy/hippy-dynamic-import-plugin': 3.0.1
      '@hippy/hippy-react-refresh-webpack-plugin': ^0.5.5
      '@hippy/react': file:third-party/@hippy/react
      '@hippy/rejection-tracking-polyfill': ^1.0.0
      '@hippy/rmc-list-view': latest
      '@hippy/rmc-pull-to-refresh': latest
      '@types/crypto-js': ^4.2.2
      '@types/google-libphonenumber': ^7.4.30
      '@types/lodash': ^4.14.149
      '@types/markdown-it': ^10.0.1
      '@types/node': ^22.14.1
      '@types/react': ^16.8.25
      '@types/react-reconciler': ^0.18.0
      '@typescript-eslint/eslint-plugin': ^3.9.1
      '@typescript-eslint/parser': ^3.9.1
      abc-fed-build-tool: ^0.7.9
      animated-scroll-to: ^2.2.0
      babel-loader: ^8.1.0
      bezier-easing: ^2.1.0
      big.js: ^6.2.2
      case-sensitive-paths-webpack-plugin: ^2.2.0
      clean-webpack-plugin: ^4.0.0
      copy-webpack-plugin: ^5.1.1
      cross-env: ^7.0.3
      cross-env-os: ^7.1.1
      crypto-js: ^4.2.0
      css-loader: ^3.4.0
      eslint: ^7.7.0
      eslint-config-prettier: ^6.11.0
      eslint-plugin-import: ^2.22.0
      eslint-plugin-jsx-a11y: ^6.3.1
      eslint-plugin-prettier: ^3.1.4
      eslint-plugin-react: ^7.20.6
      eslint-plugin-react-hooks: ^4.1.0
      file-loader: ^5.0.2
      google-libphonenumber: ^3.2.33
      html-webpack-plugin: ^5.6.3
      husky: ^8.0.0
      i18next: ^23.4.2
      lerna: ^4.0.0
      lint-staged: ^10.3.0
      lodash: ^4.17.15
      markdown-it: 12.3.2
      moment: ^2.29.4
      plop: ^4.0.1
      prettier: ^2.0.5
      react: ^17.0.2
      react-dom: ^17.0.2
      react-reconciler: file:third-party/react-reconciler
      react-refresh: ^0.11.0
      react-router: ~5.1.2
      react-router-dom: ~5.1.2
      reflect-metadata: ^0.1.13
      regenerator-runtime: ^0.13.3
      rimraf: ^6.0.1
      rxjs: ^6.5.4
      source-map: ^0.7.3
      source-map-loader: ^0.2.4
      style-loader: ^1.0.2
      terser-webpack-plugin: ^4.2.3
      ts-loader: ^6.2.1
      tslib: ^2.8.1
      turbo: ^1.13.0
      typescript: ^3.8.3
      unicode-loader: ^1.0.7
      url: ^0.11.4
      url-loader: ^3.0.0
      webpack: ^5.98.0
      webpack-cli: '5'
      webpack-dev-server: ^4.15.2
      webpack-oss: ^2.1.6
      webpackbar: ^6.0.1
    dependencies:
      '@app/abc-mobile-ui': link:packages/abc-mobile-ui
      '@app/theme': link:packages/theme
      '@hippy/hippy-dynamic-import-plugin': 3.0.1_webpack@5.100.1
      '@hippy/react': file:third-party/@hippy/react
      '@hippy/rmc-list-view': 1.0.0
      '@hippy/rmc-pull-to-refresh': 1.2.0
      '@types/node': 22.16.4
      animated-scroll-to: 2.3.2
      big.js: 6.2.2
      crypto-js: 4.2.0
      google-libphonenumber: 3.2.42
      i18next: 23.16.8
      lodash: 4.17.21
      markdown-it: 12.3.2
      moment: 2.30.1
      react: 17.0.2
      react-dom: 17.0.2_react@17.0.2
      react-reconciler: file:third-party/react-reconciler_react@17.0.2
      react-router: 5.1.2_react@17.0.2
      react-router-dom: 5.1.2_react@17.0.2
      reflect-metadata: 0.1.14
      regenerator-runtime: 0.13.11
      rxjs: 6.6.7
      tslib: 2.8.1
      url: 0.11.4
    devDependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-proposal-class-properties': 7.18.6_@babel+core@7.28.0
      '@babel/plugin-proposal-decorators': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6_@babel+core@7.28.0
      '@babel/plugin-proposal-object-rest-spread': 7.20.7_@babel+core@7.28.0
      '@babel/plugin-proposal-optional-chaining': 7.21.0_@babel+core@7.28.0
      '@babel/polyfill': 7.12.1
      '@babel/preset-env': 7.28.0_@babel+core@7.28.0
      '@babel/preset-react': 7.27.1_@babel+core@7.28.0
      '@babel/preset-typescript': 7.27.1_@babel+core@7.28.0
      '@hippy/debug-server-next': 0.5.6_c3wiets6gdmjqx6buqkbecgyqq
      '@hippy/hippy-react-refresh-webpack-plugin': 0.5.8_yuti7zaylfuj7v7av74kyezjc4
      '@hippy/rejection-tracking-polyfill': 1.0.0
      '@types/crypto-js': 4.2.2
      '@types/google-libphonenumber': 7.4.30
      '@types/lodash': 4.17.20
      '@types/markdown-it': 10.0.3
      '@types/react': 16.14.65
      '@types/react-reconciler': 0.18.0
      '@typescript-eslint/eslint-plugin': 3.10.1_kxujzhw6vbtdri44htsqryf25e
      '@typescript-eslint/parser': 3.10.1_2de3j2mqba4wgeuiaqz2k7syrm
      abc-fed-build-tool: 0.7.9_co@4.6.0+webpack@5.100.1
      babel-loader: 8.4.1_avnu35bd2ytylp2v4gms37f63m
      bezier-easing: 2.1.0
      case-sensitive-paths-webpack-plugin: 2.4.0
      clean-webpack-plugin: 4.0.0_webpack@5.100.1
      copy-webpack-plugin: 5.1.2_webpack@5.100.1
      cross-env: 7.0.3
      cross-env-os: 7.1.1
      css-loader: 3.6.0_webpack@5.100.1
      eslint: 7.32.0
      eslint-config-prettier: 6.15.0_eslint@7.32.0
      eslint-plugin-import: 2.32.0_6vpcbutzmq5vtliwzwjgeopqvm
      eslint-plugin-jsx-a11y: 6.10.2_eslint@7.32.0
      eslint-plugin-prettier: 3.4.1_6l26irxuevddeh5uhyzqivbl64
      eslint-plugin-react: 7.37.5_eslint@7.32.0
      eslint-plugin-react-hooks: 4.6.2_eslint@7.32.0
      file-loader: 5.1.0_webpack@5.100.1
      html-webpack-plugin: 5.6.3_webpack@5.100.1
      husky: 8.0.3
      lerna: 4.0.0
      lint-staged: 10.5.4
      plop: 4.0.1
      prettier: 2.8.8
      react-refresh: 0.11.0
      rimraf: 6.0.1
      source-map: 0.7.4
      source-map-loader: 0.2.4
      style-loader: 1.3.0_webpack@5.100.1
      terser-webpack-plugin: 4.2.3_webpack@5.100.1
      ts-loader: 6.2.2_typescript@3.9.10
      turbo: 1.13.4
      typescript: 3.9.10
      unicode-loader: 1.0.7
      url-loader: 3.0.0_67tl4ki5pwptdulvktxqujria4
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-cli: 5.1.4_p2e7gummt6gr4psp4bot4sod7m
      webpack-dev-server: 4.15.2_goe534c4oany46n3ybfcwdca2e
      webpack-oss: 2.1.6_webpack@5.100.1
      webpackbar: 6.0.1_webpack@5.100.1

  packages/abc-mobile-ui:
    specifiers:
      '@app/theme': workspace:*
      '@app/utils': workspace:*
      '@typescript-eslint/eslint-plugin': ^3.9.1
      '@typescript-eslint/parser': ^3.9.1
      clean-webpack-plugin: ^4.0.0
      eslint: ^7.7.0
      eslint-config-prettier: ^6.11.0
      eslint-plugin-prettier: ^3.1.4
      eslint-plugin-react: ^7.20.6
      prettier: ^2.0.5
      react: ^17.0.2
      react-dom: ^17.0.2
      ts-loader: ^9.5.2
      typescript: ^4.9.5
    dependencies:
      '@app/theme': link:../theme
      '@app/utils': link:../utils
      react: 17.0.2
      react-dom: 17.0.2_react@17.0.2
    devDependencies:
      '@typescript-eslint/eslint-plugin': 3.10.1_rn5bylxdotrtoiyaug6uygcpa4
      '@typescript-eslint/parser': 3.10.1_jofidmxrjzhj7l6vknpw5ecvfe
      clean-webpack-plugin: 4.0.0_webpack@5.100.1
      eslint: 7.32.0
      eslint-config-prettier: 6.15.0_eslint@7.32.0
      eslint-plugin-prettier: 3.4.1_6l26irxuevddeh5uhyzqivbl64
      eslint-plugin-react: 7.37.5_eslint@7.32.0
      prettier: 2.8.8
      ts-loader: 9.5.2_4iuxj5actolkhzduw7422g664a
      typescript: 4.9.5

  packages/theme:
    specifiers:
      '@app/utils': workspace:*
      '@types/node': ^22.14.1
      clean-webpack-plugin: ^4.0.0
      lodash: ^4.17.15
      react: ^17.0.2
      react-dom: ^17.0.2
      ts-loader: ^9.5.2
      typescript: ^4.9.5
    dependencies:
      '@app/utils': link:../utils
      lodash: 4.17.21
      react: 17.0.2
      react-dom: 17.0.2_react@17.0.2
    devDependencies:
      '@types/node': 22.16.4
      clean-webpack-plugin: 4.0.0_webpack@5.100.1
      ts-loader: 9.5.2_4iuxj5actolkhzduw7422g664a
      typescript: 4.9.5

  packages/utils:
    specifiers:
      '@types/node': ^22.14.1
      clean-webpack-plugin: ^4.0.0
      react: ^17.0.2
      react-dom: ^17.0.2
      ts-loader: ^9.5.2
      typescript: ^4.9.5
    dependencies:
      react: 17.0.2
      react-dom: 17.0.2_react@17.0.2
    devDependencies:
      '@types/node': 22.16.4
      clean-webpack-plugin: 4.0.0_webpack@5.100.1
      ts-loader: 9.5.2_4iuxj5actolkhzduw7422g664a
      typescript: 4.9.5

packages:

  /@ampproject/remapping/2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
    dev: true

  /@babel/code-frame/7.12.11:
    resolution: {integrity: sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=}
    dependencies:
      '@babel/highlight': 7.25.9
    dev: true

  /@babel/code-frame/7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1
    dev: true

  /@babel/compat-data/7.28.0:
    resolution: {integrity: sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core/7.24.4:
    resolution: {integrity: sha512-MBVlMXP+kkl5394RBLSxxk/iLTeVGuXTV3cIDXavPpMMqnSnt6apKgan/U8O3USWZCWZT/TbgfEpKa4uMgN4Dg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.24.4
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.24.4
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.24.0
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/core/7.28.0:
    resolution: {integrity: sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.28.0
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator/7.28.0:
    resolution: {integrity: sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.1
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
      jsesc: 3.1.0
    dev: true

  /@babel/helper-annotate-as-pure/7.27.3:
    resolution: {integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.28.1
    dev: true

  /@babel/helper-compilation-targets/7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: true

  /@babel/helper-create-class-features-plugin/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1_@babel+core@7.28.0
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.28.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-create-regexp-features-plugin/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      regexpu-core: 6.2.0
      semver: 6.3.1
    dev: true

  /@babel/helper-define-polyfill-provider/0.6.5_@babel+core@7.28.0:
    resolution: {integrity: sha512-uJnGFcPsWQK8fvjgGP5LZUZZsYGIoPeRjSF5PGwrelYgq7Q15/Ft9NGFp1zglwgIv//W0uG4BevRuSJRyylZPg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      debug: 4.4.1
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-globals/7.28.0:
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-member-expression-to-functions/7.27.1:
    resolution: {integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-module-imports/7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-module-transforms/7.27.3_@babel+core@7.24.4:
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.24.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-module-transforms/7.27.3_@babel+core@7.28.0:
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-optimise-call-expression/7.27.1:
    resolution: {integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.28.1
    dev: true

  /@babel/helper-plugin-utils/7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-remap-async-to-generator/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-wrap-function': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-replace-supers/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    resolution: {integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-string-parser/7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-identifier/7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-option/7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-wrap-function/7.27.1:
    resolution: {integrity: sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helpers/7.27.6:
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.1
    dev: true

  /@babel/highlight/7.25.9:
    resolution: {integrity: sha512-llL88JShoCsth8fF8R4SJnIn+WLvR6ccFxu1H3FlMhDontdcmZWf2HgIZ7AIqV3Xcck1idlohrN4EUBQz6klbw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.1.1
    dev: true

  /@babel/parser/7.24.4:
    resolution: {integrity: sha512-zTvEBcghmeBma9QIGunWevvBAp4/Qu9Bdq+2k0Ot4fVMD6v3dsC9WOcRSKk7tRRyBM/53yKMJko9xOatGQAwSg==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.24.0
    dev: true

  /@babel/parser/7.28.0:
    resolution: {integrity: sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.28.1
    dev: true

  /@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-QPG3C9cCVRQLxAVwmefEmwdTanECuUBMQZ/ym5kiw3XKCGA7qkuQLcjWWHcrD/GKbn/WmJwaezfuuAOcyKlRPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-bugfix-safari-class-field-initializer-scope/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-qNeq3bCKnGgLkEXUuFry6dPlGfCdQNZbn7yUAPCInwAJHMU7THJfrBSozkcWq5sNM6RcF3S8XyQL2A52KNR9IA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-g4L7OYun04N1WyqMNjldFwlfPCLVkgB54A/YCXICZYBsvJJE3kByKv9c9+R/nAfmIfjl2rKYLNyMHboYbZaWaA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-oO02gcONcD5O1iTLi/6frMJBIwWEHceWGSGqrpCmEL8nogiS6J9PBlE48CaK20/Jx1LuRml9aDftLgdjXT8+Cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-transform-optional-chaining': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-6BpaYGDavZqkI6yT+KSPdpZFfpnd68UKXbcjI9pJ13pvHhPrCKWOOLp+ysvMeA+DxnhuPpgIaRpxRxo5A9t5jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-class-properties/7.18.6_@babel+core@7.28.0:
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-decorators/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-zOiZqvANjWDUaUS9xMxbMcK/Zccztbe/6ikvUXaG9nsPH3w6qh5UaPGAnirI/WhIbZ8m3OHU0ReyPrknG+ZKeg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-decorators': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-nullish-coalescing-operator/7.18.6_@babel+core@7.28.0:
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.28.0
    dev: true

  /@babel/plugin-proposal-object-rest-spread/7.20.7_@babel+core@7.28.0:
    resolution: {integrity: sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.28.0
      '@babel/plugin-transform-parameters': 7.27.7_@babel+core@7.28.0
    dev: true

  /@babel/plugin-proposal-optional-chaining/7.21.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2_@babel+core@7.28.0:
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
    dev: true

  /@babel/plugin-syntax-decorators/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-YMq8Z87Lhl8EGkmb0MwYkt36QnxC+fzCgrl66ereamPlYToRpIk5nUjKUY3QKLWq8mwUB1BgbeXcTJhZOCDg5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-import-assertions/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-import-attributes/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-jsx/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.28.0:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.28.0:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.28.0:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-typescript/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-unicode-sets-regex/7.18.6_@babel+core@7.28.0:
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-arrow-functions/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-async-generator-functions/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-BEOdvX4+M765icNPZeidyADIvQ1m1gmunXufXxvRESy/jNNyfovIqUyE7MVgGBjWktCoJlzvFA1To2O4ymIO3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1_@babel+core@7.28.0
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-async-to-generator/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-block-scoped-functions/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-cnqkuOtZLapWYZUYM5rVIdv1nXYuFVIltZ6ZJ7nIj585QsjKM5dhL2Fu/lICXZ1OyIAFc7Qy+bvDAtTXqGrlhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-block-scoping/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-gKKnwjpdx5sER/wl0WN0efUBFzF/56YZO0RJrSYP4CljXnP31ByY7fol89AzomdlLNzI36AvOTmYHsnZTCkq8Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-class-properties/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-D0VcalChDMtuRvJIu3U/fwWjf8ZMykz5iZsg77Nuj821vCKI3zCyRLwRdWbsuJ/uRwZhZ002QtCqIkwC/ZkvbA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-class-static-block/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-s734HmYU78MVzZ++joYM+NkJusItbdRcbm+AGRgJCt3iA+yux0QpD9cBVdz3tKyrjVYWRl7j0mHSmv4lhV0aoA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-classes/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-IjM1IoJNw72AZFlj33Cu8X0q2XK/6AaVC3jQu+cgQ5lThWD5ajnuUAml80dqRmOhmPkTH8uAwnpMu9Rvj0LTRA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-globals': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1_@babel+core@7.28.0
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-computed-properties/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-lj9PGWvMTVksbWiDT2tW68zGS/cyo4AkZ/QTp0sQT0mjPopCmrSkzxeXkznjqBxzDI6TclZhOJbBmbBLjuOZUw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2
    dev: true

  /@babel/plugin-transform-destructuring/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-v1nrSMBiKcodhsyJ4Gf+Z0U/yawmJDBOTpEB3mcQY52r9RIyPneGyAS/yM6seP/8I+mWI3elOMtT5dB8GJVs+A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-dotall-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-duplicate-keys/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-MTyJk98sHvSs+cvZ4nOauwTTG1JeonDjSGvGGUNHreGQns+Mpt6WX/dVzWBHgg+dYZhkC4X+zTDfkTU+Vy9y7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-duplicate-named-capturing-groups-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-hkGcueTEzuhB30B3eJCbCYeCaaEQOmQR0AdvzpD4LoN0GXMWzzGSuRrxR2xTnCrvNbVwK9N6/jQ92GSLfiZWoQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-dynamic-import/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-MHzkWQcEmjzzVW9j2q8LGjwGWpG2mjwaaB0BNQwst3FIjqsg8Ct/mIZlvSPJvfi9y2AC8mi/ktxbFVL9pZ1I4A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-explicit-resource-management/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-K8nhUcn3f6iB+P3gwCv/no7OdzOZQcKchW6N389V6PD8NUWKZHzndOd9sPDVbMoBsbmjMqlB4L9fm+fEFNVlwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.28.0_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-exponentiation-operator/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-uspvXnhHvGKf2r4VVtBpeFnuDWsJLQ6MF6lGJLC89jBR1uoVeqM416AZtTuhTezOfgHicpJQmoD5YUakO/YmXQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-export-namespace-from/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-tQvHWSZ3/jH2xuq/vZDy0jNn+ZdXJeM8gHvX4lnJmsc3+50yPlWdZXIc5ay+umX+2/tJIqHqiEqcJvxlmIvRvQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-for-of/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-function-name/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-json-strings/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-6WVLVJiTjqcQauBhn1LkICsR2H+zm62I3h9faTDKt1qP4jn2o72tSvqMwtGFKGTpojce0gJs+76eZ2uCHRZh0Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-literals/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-logical-assignment-operators/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-SJvDs5dXxiae4FbSL1aBJlG4wvl594N6YEVVn9e3JGulwioy6z3oPjx/sQBO3Y4NwUu5HNix6KJ3wBZoewcdbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-member-expression-literals/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-modules-amd/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-commonjs/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-systemjs/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-w5N1XzsRbc0PQStASMksmUeqECuzKuTJer7kFagK8AXgpCMkeDMO5S+aaFb7A51ZYDF7XI34qsTX+fkHiIm5yA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-umd/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-iQBE/xC5BV1OxJbp6WG7jq9IWiD+xxlZhLrdwpPkTX3ydmXdvoCpyfJN7acaIBZaOqTfr76pgzqBJflNbeRK+w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-named-capturing-groups-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-new-target/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-nullish-coalescing-operator/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-aGZh6xMo6q9vq1JGcw58lZ1Z0+i0xB2x0XaauNIUXd6O1xXc3RwoWEBlsTQrY4KQ9Jf0s5rgD6SiNkaUdJegTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-numeric-separator/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-fdPKAcujuvEChxDBJ5c+0BTaS6revLV7CJL08e4m3de8qJfNIuCc2nc7XJYOjBoTMJeqSmwXJ0ypE14RCjLwaw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-object-rest-spread/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-9VNGikXxzu5eCiQjdE4IZn8sb9q7Xsk5EXLDBKUYg1e/Tve8/05+KJEtcxGxAgCY5t/BpKQM+JEL/yT4tvgiUA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-parameters': 7.27.7_@babel+core@7.28.0
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-object-super/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-optional-catch-binding/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-txEAEKzYrHEX4xSZN4kJ+OfKXFVSWKB2ZxM9dpcE3wT7smwkNmXo5ORRlVzMVdJbD+Q8ILTgSD7959uj+3Dm3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-optional-chaining/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-BQmKPPIuc8EkZgNKsv0X4bPmOoayeu4F1YCwx2/CfmDSXDbp7GnzlUH+/ul5VGfRg1AoFPsrIThlEBj2xb4CAg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-parameters/7.27.7_@babel+core@7.28.0:
    resolution: {integrity: sha512-qBkYTYCb76RRxUM6CcZA5KRu8K4SM8ajzVeUgVdMVO9NN9uI/GaVmBg/WKJJGnNokV9SY8FxNOVWGXzqzUidBg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-private-methods/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-10FVt+X55AjRAYI9BrdISN9/AQWHqldOeZDUoLyif1Kn05a56xVBXb8ZouL8pZ9jem8QpXaOt8TS7RHUIS+GPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-private-property-in-object/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-5J+IhqTi1XPa0DXF83jYOaARrX+41gOewWbkPyjMNRDqgOCqdffGh8L3f/Ek5utaEBZExjSAzcyjmV9SSAWObQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-property-literals/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-react-display-name/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-D6Eujc2zMxKjfa4Zxl4GHMsmhKKZ9VpcqIchJLvwTxad9zWIYulwYItBovpDOoNLISpcZSXoDJ5gaGbQUDqViA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-react-jsx-development/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-ykDdF5yI4f1WrAolLqeF3hmYU12j9ntLQl/AOG1HAS21jxyg1Q0/J/tpREuYLfatGdGmXp/3yS0ZA76kOlVq9Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-transform-react-jsx': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-react-jsx/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.28.0
      '@babel/types': 7.28.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-react-pure-annotations/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-JfuinvDOsD9FVMTHpzA/pBLisxpv1aSf+OIV8lgH3MuWrks19R27e6a6DipIg4aX1Zm9Wpb04p8wljfKrVSnPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-regenerator/7.28.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-P0QiV/taaa3kXpLY+sXla5zec4E+4t4Aqc9ggHlfZ7a2cp8/x/Gv08jfwEtn9gnnYIMvHx6aoOZ8XJL8eU71Dg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-regexp-modifiers/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-TtEciroaiODtXvLZv4rmfMhkCv8jx3wgKpL68PuiPh2M4fvz5jhsA7697N1gMvkvr/JTF13DrFYyEbY9U7cVPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-reserved-words/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-V2ABPHIJX4kC7HegLkYoDpfg9PVmuWy/i6vUM5eGK22bx4YVFD3M5F0QQnWQoDs6AGsUWTVOopBiMFQgHaSkVw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-shorthand-properties/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-spread/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-sticky-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-lhInBO5bi/Kowe2/aLdBAawijx+q1pQzicSgnkB6dUPc1+RC8QmJHKf2OjvU+NZWitguJHEaEmbV6VWEouT58g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-template-literals/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-fBJKiV7F2DxZUkg5EtHKXQdbsbURW3DZKQUWphDum0uRP6eHGGa/He9mc0mypL680pb+e/lDIthRohlv8NCHkg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-typeof-symbol/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-RiSILC+nRJM7FY5srIyc4/fGIwUhyDuuBSdWn4y6yT6gm652DpCHZjIipgn6B7MQ1ITOUnAKWixEUjQRIBIcLw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-typescript/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-4AEiDEBPIZvLQaWlc9liCavE0xRM0dNca41WtBeM3jgFptfUOSG9z0uteLhq6+3rq+WB6jIvUwKDTpXEHPJ2Vg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-syntax-typescript': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-unicode-escapes/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-Ysg4v6AmF26k9vpfFuTZg8HRfVWzsh1kVfowA23y9j/Gu6dOuahdUVhkLqpObp3JIv27MLSii6noRnuKN8H0Mg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-unicode-property-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-uW20S39PnaTImxp39O5qFlHLS9LJEmANjMG7SxIhap8rCHqu0Ik+tLEPX5DKmHn6CsWQ7j3lix2tFOa5YtL12Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-unicode-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-xvINq24TRojDuyt6JGtHmkVkrfVV3FPT16uytxImLeBZqW3/H52yN+kM1MGuyPkIQxrzKwPHs5U/MP3qKyzkGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-unicode-sets-regex/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-EtkOujbc4cgvb0mlpQefi4NTPBzhSIevblFevACNLUspmrALgmEBdL/XfnyyITfd8fKBZrZys92zOWcik7j9Tw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.28.0
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/polyfill/7.12.1:
    resolution: {integrity: sha1-Hy1jcdEmG72WHzxdWQkVDhLQvZY=}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.13.11
    dev: true

  /@babel/preset-env/7.28.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-VmaxeGOwuDqzLl5JUkIRM1X2Qu2uKGxHEQWh+cvvbl7JuJRgKGJSfsEF/bUaxFhJl/XAyxBe7q7qSuTbKFuCyg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2_@babel+core@7.28.0
      '@babel/plugin-syntax-import-assertions': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-syntax-import-attributes': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6_@babel+core@7.28.0
      '@babel/plugin-transform-arrow-functions': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-async-generator-functions': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-async-to-generator': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-block-scoped-functions': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-block-scoping': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-class-properties': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-class-static-block': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-classes': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-computed-properties': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-destructuring': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-dotall-regex': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-duplicate-keys': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-dynamic-import': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-explicit-resource-management': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-exponentiation-operator': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-export-namespace-from': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-for-of': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-function-name': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-json-strings': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-literals': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-logical-assignment-operators': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-member-expression-literals': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-modules-amd': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-modules-commonjs': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-modules-systemjs': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-modules-umd': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-named-capturing-groups-regex': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-new-target': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-nullish-coalescing-operator': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-numeric-separator': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-object-rest-spread': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-object-super': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-optional-catch-binding': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-optional-chaining': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-parameters': 7.27.7_@babel+core@7.28.0
      '@babel/plugin-transform-private-methods': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-private-property-in-object': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-property-literals': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-regenerator': 7.28.1_@babel+core@7.28.0
      '@babel/plugin-transform-regexp-modifiers': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-reserved-words': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-shorthand-properties': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-spread': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-sticky-regex': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-template-literals': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-typeof-symbol': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-unicode-escapes': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-unicode-property-regex': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-unicode-regex': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-unicode-sets-regex': 7.27.1_@babel+core@7.28.0
      '@babel/preset-modules': 0.1.6-no-external-plugins_@babel+core@7.28.0
      babel-plugin-polyfill-corejs2: 0.4.14_@babel+core@7.28.0
      babel-plugin-polyfill-corejs3: 0.13.0_@babel+core@7.28.0
      babel-plugin-polyfill-regenerator: 0.6.5_@babel+core@7.28.0
      core-js-compat: 3.44.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/preset-modules/0.1.6-no-external-plugins_@babel+core@7.28.0:
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/types': 7.28.1
      esutils: 2.0.3
    dev: true

  /@babel/preset-react/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-oJHWh2gLhU9dW9HHr42q0cI0/iHHXTLGe39qvpAZZzagHy0MzYLCnCVV0symeRvzmjHyVU7mw2K06E6u/JwbhA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-transform-react-display-name': 7.28.0_@babel+core@7.28.0
      '@babel/plugin-transform-react-jsx': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-react-jsx-development': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-react-pure-annotations': 7.27.1_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/preset-typescript/7.27.1_@babel+core@7.28.0:
    resolution: {integrity: sha512-l7WfQfX0WK4M0v2RudjuQK4u99BS6yLHYEmdtVPP7lKV013zr9DygFuWNlnbvQ9LR+LS0Egz/XAvGx5U9MX0fQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-modules-commonjs': 7.27.1_@babel+core@7.28.0
      '@babel/plugin-transform-typescript': 7.28.0_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/runtime/7.27.6:
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/template/7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.1
    dev: true

  /@babel/traverse/7.28.0:
    resolution: {integrity: sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/types': 7.28.1
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types/7.24.0:
    resolution: {integrity: sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      to-fast-properties: 2.0.0
    dev: true

  /@babel/types/7.28.1:
    resolution: {integrity: sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
    dev: true

  /@colors/colors/1.6.0:
    resolution: {integrity: sha512-Ir+AOibqzrIsL6ajt3Rz3LskB7OiMVHqltZmspbW/TJuTVuyOMirVqAkjfY6JISiLHgyNqicAC8AyHHGzNd/dA==}
    engines: {node: '>=0.1.90'}
    dev: true

  /@dabh/diagnostics/2.0.3:
    resolution: {integrity: sha512-hrlQOIi7hAfzsMqlGSFyVucrx38O+j6wiGOf//H2ecvIEqYN4ADBSS2iLMh5UFyDunCNniUIPk/q3riFv45xRA==}
    dependencies:
      colorspace: 1.1.4
      enabled: 2.0.0
      kuler: 2.0.0
    dev: true

  /@discoveryjs/json-ext/0.5.7:
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}

  /@eslint/eslintrc/0.4.3:
    resolution: {integrity: sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 7.3.1
      globals: 13.24.0
      ignore: 4.0.6
      import-fresh: 3.3.1
      js-yaml: 3.14.1
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@gar/promisify/1.1.3:
    resolution: {integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==}
    dev: true

  /@hapi/bourne/3.0.0:
    resolution: {integrity: sha512-Waj1cwPXJDucOib4a3bAISsKJVb15MKi9IvmTI/7ssVEm6sywXGjVJDhl6/umt1pK1ZS7PacXU3A1PmFKHEZ2w==}
    dev: true

  /@hippy/chrome-devtools-extensions/0.5.0:
    resolution: {integrity: sha512-Z0alo6FiY5OcCSeugaC934FY/q8Ge/2zE6H3gOS9jXAkycTat21efuFBQC4jRNIeNJLJHJ5LHc5gPZcZ/9QS1A==}
    dev: true

  /@hippy/chrome-devtools/0.1.2:
    resolution: {integrity: sha512-iwJhsvQ5MiYpB68JVOgfiUMXL7IG8jrWu6quoAcODFatsQi8JPHm2VB+WymXiJnbbpdwL6WTDHFAvcOXQRYu0Q==}
    dev: true

  /@hippy/debug-server-next/0.5.6_c3wiets6gdmjqx6buqkbecgyqq:
    resolution: {integrity: sha512-++dTs+Ye7es7+fWr3sPpQyYimOTWnmuReLn928OaU//U22epnm/PNUfiIEp+UMR4UQ+Z6bbtFUFmRVcPQY2+5A==}
    engines: {node: '>=16.0.0'}
    hasBin: true
    dependencies:
      '@hippy/chrome-devtools': 0.1.2
      '@hippy/chrome-devtools-extensions': 0.5.0
      '@hippy/devtools-protocol': 0.0.4
      '@hippy/hippy-hmr-plugin': 0.1.1_webpack@5.100.1
      '@hippy/hippy-react-devtools-plugin': 0.1.3_webpack@5.100.1
      '@hippy/hippy-vue-devtools-plugin': 0.1.0_webpack@5.100.1
      '@hippy/react-devtools': 0.1.0
      '@hippy/vanilla-js-devtools': 0.1.13
      '@hippy/vue-devtools': 0.0.1
      '@koa/cors': 3.4.3
      axios: 0.26.1
      bonjour: 3.5.0
      chokidar: 3.5.3
      color-normalize: 1.5.2
      colors: 1.4.0
      compression: 1.8.0
      connect-history-api-fallback: 1.6.0
      cos-nodejs-sdk-v5: 2.15.1
      default-gateway: 6.0.3
      del: 6.1.1
      detect-port: 1.6.1
      dotenv: 10.0.0
      express: 4.21.2
      graceful-fs: 4.2.11
      http-proxy-middleware: 2.0.9
      ipaddr.js: 2.2.0
      kill-port: 1.6.1
      koa: 2.16.1
      koa-bodyparser: 4.4.1
      koa-router: 10.1.1
      koa-static: 5.0.0
      koa-static-cache: 5.1.4
      lodash: 4.17.21
      module-alias: 2.2.3
      node-machine-id: 1.1.12
      open: 8.4.2
      p-retry: 4.6.2
      portfinder: 1.0.37
      qrcode: 1.5.4
      redis: 4.0.0-rc.4
      request-promise: 4.2.6_request@2.88.2
      schema-utils: 4.3.2
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      tslib: 2.8.1
      url: 0.11.4
      webpack-dev-middleware: 5.3.4_webpack@5.100.1
      winston: 3.17.0
      winston-daily-rotate-file: 4.7.1_winston@3.17.0
      ws: 8.18.3
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/express'
      - bufferutil
      - debug
      - request
      - supports-color
      - utf-8-validate
      - webpack
    dev: true

  /@hippy/devtools-protocol/0.0.4:
    resolution: {integrity: sha512-wGUWt7OtQF51EzVoTAW6weY1bUCLvIQLnpyElT5ADt4IeP76nDpqYBRPZlNbZhYVaBOzMjTEs9VRZ0S3oevJHg==}
    dev: true

  /@hippy/hippy-dynamic-import-plugin/3.0.1_webpack@5.100.1:
    resolution: {integrity: sha512-WxpiVGFtNguw3sSkW76Y5lfXFXlWRhZPxRqbXPTHSeRgfy+3JFvjU2eQJANWdel2aUQgxBCI8syBj2Ul679qIw==}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      tapable: 2.2.2
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: false

  /@hippy/hippy-hmr-plugin/0.1.1_webpack@5.100.1:
    resolution: {integrity: sha512-QnNhtgDOyA7WvZg4vx7RIXD7Ptrh6SHmAqaT891L4NTL/R6UcRIjOn4iN2P0aPmuUf4bvbY+9yOODOVXY/TZzg==}
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
    dependencies:
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /@hippy/hippy-react-devtools-plugin/0.1.3_webpack@5.100.1:
    resolution: {integrity: sha512-cKBexhLTtqbP4b0l4WD29Kt03opFNtRZDPozGbpwn9anUIuFHoN0d3WJaZIKKKlwMpMrsjoNov/NwRFkzqN5Zg==}
    peerDependencies:
      webpack: ^4.37.0 || ^5.24.2
    dependencies:
      schema-utils: 3.1.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /@hippy/hippy-react-refresh-webpack-plugin/0.5.8_yuti7zaylfuj7v7av74kyezjc4:
    resolution: {integrity: sha512-OsLiNoQ3zQTVUfzUC4g9jGf1QAVWln7PRYDRuTFPkEHUwodaB/c5zlSU57kb0cfr/kxECKGlfBveELuK3MH1fQ==}
    engines: {node: '>= 10.13'}
    peerDependencies:
      '@types/webpack': 4.x || 5.x
      react-refresh: '>=0.10.0 <1.0.0'
      sockjs-client: ^1.4.0
      type-fest: '>=0.17.0 <3.0.0'
      webpack: '>=4.43.0 <6.0.0'
      webpack-dev-server: 3.x || 4.x
      webpack-hot-middleware: 2.x
      webpack-plugin-serve: 0.x || 1.x
    peerDependenciesMeta:
      '@types/webpack':
        optional: true
      sockjs-client:
        optional: true
      type-fest:
        optional: true
      webpack-dev-server:
        optional: true
      webpack-hot-middleware:
        optional: true
      webpack-plugin-serve:
        optional: true
    dependencies:
      ansi-html-community: 0.0.8
      common-path-prefix: 3.0.0
      core-js-pure: 3.44.0
      error-stack-parser: 2.1.4
      find-up: 5.0.0
      html-entities: 2.6.0
      loader-utils: 2.0.4
      react-refresh: 0.11.0
      schema-utils: 3.3.0
      source-map: 0.7.4
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-dev-server: 4.15.2_goe534c4oany46n3ybfcwdca2e
    dev: true

  /@hippy/hippy-vue-devtools-plugin/0.1.0_webpack@5.100.1:
    resolution: {integrity: sha512-1XfmvF161Akjuwf0lQndhawck0K7mTbPZOueZGwRWt9gFB8WdWdn5zv5x4jDrYUmE/hbPBTiFoVleVzGQJ/VEQ==}
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
    dependencies:
      schema-utils: 3.1.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /@hippy/react-devtools/0.1.0:
    resolution: {integrity: sha512-sgsOjY08DmyBJdYIDv1t9vhiSnVwuiqoYiU9x2GoeGxmRTzqXWHH1sb7HtFVk12giQ4ZKUGJzHv3B1JBuvLB5g==}
    dev: true

  /@hippy/react-reconciler/0.26.4_react@17.0.2:
    resolution: {integrity: sha512-CN8W5jybiJxjBGpM0pcGm9OpRcGas6lrGDV5j9/DThEIuXWeuHAErhZHIpvajIR+NUd5VW/6CWnth96EYllR0w==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      react: ^17.0.2
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react: 17.0.2
      scheduler: 0.20.2
    dev: false

  /@hippy/rejection-tracking-polyfill/1.0.0:
    resolution: {integrity: sha512-iJ/J+n6kyb1rblB7e563vU1VgXxGTUy8TGzcEACUIOt8VLwnxPZGcHDkYW3SQhUB0YF+9dO6QLCX83iGlQqvSA==}
    dev: true

  /@hippy/rmc-list-view/1.0.0:
    resolution: {integrity: sha512-WNwBWLb275nZYyIUjxki+HUBeNJS12jrLDP9iiHfC8ChjPZzrAfTjYu6Cep/oTYfmyEujHEGQmEjf6pTSyAEZw==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
      fbjs: 3.0.5
      prop-types: 15.8.1
      warning: 3.0.0
      zscroller: 0.4.8
    transitivePeerDependencies:
      - encoding
    dev: false

  /@hippy/rmc-pull-to-refresh/1.2.0:
    resolution: {integrity: sha512-39+u72ucIUPfERZNXBHYFheoSM7xjsovD0Qu5M+rohtuHD2GjcowavetMigKrCaS7LwGq5uQEdRstZ2nUT9hBg==}
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
    dev: false

  /@hippy/vanilla-js-devtools/0.1.13:
    resolution: {integrity: sha512-LC5aTrGauIJBTfjYUddAP4kwdQqrSvIUk0TZxFK4JR3OQXE/8+3BRnw/t0rfg8Gl7AooWm1PgCBkyPXxWzLeDA==}
    dev: true

  /@hippy/vue-devtools/0.0.1:
    resolution: {integrity: sha512-H1HiTMWE6oEzBIRlLlsNdUeEEmJZZrQwSPXTCMjN/22MrGnSTmcnD0GBRLNRpR0xlWpKtGn8VaTo0/JK3P+y6w==}
    engines: {node: '>=8.10'}
    dev: true

  /@humanwhocodes/config-array/0.5.0:
    resolution: {integrity: sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=}
    engines: {node: '>=10.10.0'}
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/object-schema/1.2.1:
    resolution: {integrity: sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=}
    dev: true

  /@hutson/parse-repository-url/3.0.2:
    resolution: {integrity: sha1-mMI8lQo9m2yPDa7QbabDrwaYE0A=}
    engines: {node: '>=6.9.0'}
    dev: true

  /@inquirer/figures/1.0.12:
    resolution: {integrity: sha512-MJttijd8rMFcKJC8NYmprWr6hD3r9Gd9qUC0XwPNwoEPWSMVJwA2MlXxF+nhZZNMY+HXsWa+o7KY2emWYIn0jQ==}
    engines: {node: '>=18'}
    dev: true

  /@isaacs/balanced-match/4.0.1:
    resolution: {integrity: sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ==}
    engines: {node: 20 || >=22}
    dev: true

  /@isaacs/brace-expansion/5.0.0:
    resolution: {integrity: sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA==}
    engines: {node: 20 || >=22}
    dependencies:
      '@isaacs/balanced-match': 4.0.1
    dev: true

  /@isaacs/cliui/8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width/4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: /strip-ansi/6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi/7.0.0
    dev: true

  /@jridgewell/gen-mapping/0.3.12:
    resolution: {integrity: sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29

  /@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/source-map/0.3.10:
    resolution: {integrity: sha512-0pPkgz9dY+bijgistcTTJ5mR+ocqRXLuhXHYdzoMmmoJ2C9S46RCm2GMUbatPEUK9Yjy26IrAy8D/M00lLkv+Q==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  /@jridgewell/sourcemap-codec/1.5.4:
    resolution: {integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==}

  /@jridgewell/trace-mapping/0.3.29:
    resolution: {integrity: sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4

  /@koa/cors/3.4.3:
    resolution: {integrity: sha512-WPXQUaAeAMVaLTEFpoq3T2O1C+FstkjJnDQqy95Ck1UdILajsRhu6mhJ8H2f4NFPRBoCNN+qywTJfq/gGki5mw==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      vary: 1.1.2
    dev: true

  /@leichtgewicht/ip-codec/2.0.5:
    resolution: {integrity: sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw==}

  /@lerna/add/4.0.0:
    resolution: {integrity: sha1-w29X0TJQKle55wWNFUi3pWXvGD8=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/bootstrap': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/filter-options': 4.0.0
      '@lerna/npm-conf': 4.0.0
      '@lerna/validation-error': 4.0.0
      dedent: 0.7.0
      npm-package-arg: 8.1.5
      p-map: 4.0.0
      pacote: 11.3.5
      semver: 7.7.2
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /@lerna/bootstrap/4.0.0:
    resolution: {integrity: sha1-X1xeLGz8j87FDLL75WmoxgcQGJE=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/command': 4.0.0
      '@lerna/filter-options': 4.0.0
      '@lerna/has-npm-version': 4.0.0
      '@lerna/npm-install': 4.0.0
      '@lerna/package-graph': 4.0.0
      '@lerna/pulse-till-done': 4.0.0
      '@lerna/rimraf-dir': 4.0.0
      '@lerna/run-lifecycle': 4.0.0
      '@lerna/run-topologically': 4.0.0
      '@lerna/symlink-binary': 4.0.0
      '@lerna/symlink-dependencies': 4.0.0
      '@lerna/validation-error': 4.0.0
      dedent: 0.7.0
      get-port: 5.1.1
      multimatch: 5.0.0
      npm-package-arg: 8.1.5
      npmlog: 4.1.2
      p-map: 4.0.0
      p-map-series: 2.1.0
      p-waterfall: 2.1.1
      read-package-tree: 5.3.1
      semver: 7.7.2
    dev: true

  /@lerna/changed/4.0.0:
    resolution: {integrity: sha1-ufx2zqObkpKmzSY/A+tXr4XJJws=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/collect-updates': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/listable': 4.0.0
      '@lerna/output': 4.0.0
    dev: true

  /@lerna/check-working-tree/4.0.0:
    resolution: {integrity: sha1-JX42pgLAAULnYIKhk1jj4a6NvVg=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/collect-uncommitted': 4.0.0
      '@lerna/describe-ref': 4.0.0
      '@lerna/validation-error': 4.0.0
    dev: true

  /@lerna/child-process/4.0.0:
    resolution: {integrity: sha1-NBuWpX3/vZcFZG0xbiMd9vpN9uE=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      chalk: 4.1.2
      execa: 5.1.1
      strong-log-transformer: 2.1.0
    dev: true

  /@lerna/clean/4.0.0:
    resolution: {integrity: sha1-j3eLbyYXqiqTamteCFrmJJjlfcU=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/command': 4.0.0
      '@lerna/filter-options': 4.0.0
      '@lerna/prompt': 4.0.0
      '@lerna/pulse-till-done': 4.0.0
      '@lerna/rimraf-dir': 4.0.0
      p-map: 4.0.0
      p-map-series: 2.1.0
      p-waterfall: 2.1.1
    dev: true

  /@lerna/cli/4.0.0:
    resolution: {integrity: sha1-jqvTNFWINsFmTfI/Gay5Xpi1u/M=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/global-options': 4.0.0
      dedent: 0.7.0
      npmlog: 4.1.2
      yargs: 16.2.0
    dev: true

  /@lerna/collect-uncommitted/4.0.0:
    resolution: {integrity: sha1-hVzWRhKWk3HPwkU7kFkwU/8bp3k=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      chalk: 4.1.2
      npmlog: 4.1.2
    dev: true

  /@lerna/collect-updates/4.0.0:
    resolution: {integrity: sha1-jiCLG6/Zijcv8Rd/el4oj2vqgEE=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/describe-ref': 4.0.0
      minimatch: 3.1.2
      npmlog: 4.1.2
      slash: 3.0.0
    dev: true

  /@lerna/command/4.0.0:
    resolution: {integrity: sha1-mRx5cd+PW/aubkLICIaaVTYcG5g=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/package-graph': 4.0.0
      '@lerna/project': 4.0.0
      '@lerna/validation-error': 4.0.0
      '@lerna/write-log-file': 4.0.0
      clone-deep: 4.0.1
      dedent: 0.7.0
      execa: 5.1.1
      is-ci: 2.0.0
      npmlog: 4.1.2
    dev: true

  /@lerna/conventional-commits/4.0.0:
    resolution: {integrity: sha1-Zg+yx7cYy5QurXARDfYfGMb5l1A=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/validation-error': 4.0.0
      conventional-changelog-angular: 5.0.13
      conventional-changelog-core: 4.2.4
      conventional-recommended-bump: 6.1.0
      fs-extra: 9.1.0
      get-stream: 6.0.1
      lodash.template: 4.5.0
      npm-package-arg: 8.1.5
      npmlog: 4.1.2
      pify: 5.0.0
      semver: 7.7.2
    dev: true

  /@lerna/create-symlink/4.0.0:
    resolution: {integrity: sha1-jFMXzlron2eCVEO9dlG/QSF4Yig=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      cmd-shim: 4.1.0
      fs-extra: 9.1.0
      npmlog: 4.1.2
    dev: true

  /@lerna/create/4.0.0:
    resolution: {integrity: sha1-tpR+m137ZTAyGVKZiUjD5j1k1zA=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/npm-conf': 4.0.0
      '@lerna/validation-error': 4.0.0
      dedent: 0.7.0
      fs-extra: 9.1.0
      globby: 11.1.0
      init-package-json: 2.0.5
      npm-package-arg: 8.1.5
      p-reduce: 2.1.0
      pacote: 11.3.5
      pify: 5.0.0
      semver: 7.7.2
      slash: 3.0.0
      validate-npm-package-license: 3.0.4
      validate-npm-package-name: 3.0.0
      whatwg-url: 8.7.0
      yargs-parser: 20.2.4
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /@lerna/describe-ref/4.0.0:
    resolution: {integrity: sha1-U8U7TqZf3O/6Bypiv+vmdyxF2ew=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      npmlog: 4.1.2
    dev: true

  /@lerna/diff/4.0.0:
    resolution: {integrity: sha1-bTBxgXqqQgWge/d8/G6TJ5bUi5I=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/validation-error': 4.0.0
      npmlog: 4.1.2
    dev: true

  /@lerna/exec/4.0.0:
    resolution: {integrity: sha1-62y5XLktQlkOni1ij8r0cZ1Ki+Y=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/filter-options': 4.0.0
      '@lerna/profiler': 4.0.0
      '@lerna/run-topologically': 4.0.0
      '@lerna/validation-error': 4.0.0
      p-map: 4.0.0
    dev: true

  /@lerna/filter-options/4.0.0:
    resolution: {integrity: sha1-rJTMUV1/o7R+L3103t3qux3l6eY=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/collect-updates': 4.0.0
      '@lerna/filter-packages': 4.0.0
      dedent: 0.7.0
      npmlog: 4.1.2
    dev: true

  /@lerna/filter-packages/4.0.0:
    resolution: {integrity: sha1-sfcNcOHenN02pOUMqgrFAfjQEvI=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/validation-error': 4.0.0
      multimatch: 5.0.0
      npmlog: 4.1.2
    dev: true

  /@lerna/get-npm-exec-opts/4.0.0:
    resolution: {integrity: sha1-3JVb6UpK51w3Tvm86RMgiH00YI8=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      npmlog: 4.1.2
    dev: true

  /@lerna/get-packed/4.0.0:
    resolution: {integrity: sha1-CYnWFiSsH5fjk72tITfEnNejeCM=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      fs-extra: 9.1.0
      ssri: 8.0.1
      tar: 6.2.1
    dev: true

  /@lerna/github-client/4.0.0:
    resolution: {integrity: sha1-LO1nchNj73D44S/6/ORBCRj0qKQ=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@octokit/plugin-enterprise-rest': 6.0.1
      '@octokit/rest': 18.12.0
      git-url-parse: 11.6.0
      npmlog: 4.1.2
    transitivePeerDependencies:
      - encoding
    dev: true

  /@lerna/gitlab-client/4.0.0:
    resolution: {integrity: sha1-ANrXM3nHs4lR1LTe0ENQTBTitn0=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      node-fetch: 2.7.0
      npmlog: 4.1.2
      whatwg-url: 8.7.0
    transitivePeerDependencies:
      - encoding
    dev: true

  /@lerna/global-options/4.0.0:
    resolution: {integrity: sha1-x9iw3moB2KhF4mIeqJ5/YPGMal8=}
    engines: {node: '>= 10.18.0'}
    dev: true

  /@lerna/has-npm-version/4.0.0:
    resolution: {integrity: sha1-0/wyksVF6yi9STs25iN88CefYxw=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      semver: 7.7.2
    dev: true

  /@lerna/import/4.0.0:
    resolution: {integrity: sha1-veZWxKRR+oeuQXM/+KjaYFR8VGU=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/prompt': 4.0.0
      '@lerna/pulse-till-done': 4.0.0
      '@lerna/validation-error': 4.0.0
      dedent: 0.7.0
      fs-extra: 9.1.0
      p-map-series: 2.1.0
    dev: true

  /@lerna/info/4.0.0:
    resolution: {integrity: sha1-ufsOR51g7+FiNgOVioMaiLHX8fw=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/command': 4.0.0
      '@lerna/output': 4.0.0
      envinfo: 7.14.0
    dev: true

  /@lerna/init/4.0.0:
    resolution: {integrity: sha1-2t/2fm37mB6My+DmoxDoN5YvbHo=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/command': 4.0.0
      fs-extra: 9.1.0
      p-map: 4.0.0
      write-json-file: 4.3.0
    dev: true

  /@lerna/link/4.0.0:
    resolution: {integrity: sha1-w6OKq9RCedcU6Q8kUeMbY/D7Zbo=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/command': 4.0.0
      '@lerna/package-graph': 4.0.0
      '@lerna/symlink-dependencies': 4.0.0
      p-map: 4.0.0
      slash: 3.0.0
    dev: true

  /@lerna/list/4.0.0:
    resolution: {integrity: sha1-JLTmmVvXP4HFVnk/5QK4R+/Z0dc=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/command': 4.0.0
      '@lerna/filter-options': 4.0.0
      '@lerna/listable': 4.0.0
      '@lerna/output': 4.0.0
    dev: true

  /@lerna/listable/4.0.0:
    resolution: {integrity: sha1-0A1stICbQD8rA3T8Uhp44xiwEhQ=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/query-graph': 4.0.0
      chalk: 4.1.2
      columnify: 1.6.0
    dev: true

  /@lerna/log-packed/4.0.0:
    resolution: {integrity: sha1-lRaP4uJqxqceQvS+hXUZt35XoJ8=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      byte-size: 7.0.1
      columnify: 1.6.0
      has-unicode: 2.0.1
      npmlog: 4.1.2
    dev: true

  /@lerna/npm-conf/4.0.0:
    resolution: {integrity: sha1-sln9HhzuK/VAKyNudwFA/5ref9I=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      config-chain: 1.1.13
      pify: 5.0.0
    dev: true

  /@lerna/npm-dist-tag/4.0.0:
    resolution: {integrity: sha1-0embTszTQUFC8FSK0zG/LVPzJXo=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/otplease': 4.0.0
      npm-package-arg: 8.1.5
      npm-registry-fetch: 9.0.0
      npmlog: 4.1.2
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /@lerna/npm-install/4.0.0:
    resolution: {integrity: sha1-MRgL46s7fRgYoaDCBq7BVrcJTHg=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/get-npm-exec-opts': 4.0.0
      fs-extra: 9.1.0
      npm-package-arg: 8.1.5
      npmlog: 4.1.2
      signal-exit: 3.0.7
      write-pkg: 4.0.0
    dev: true

  /@lerna/npm-publish/4.0.0:
    resolution: {integrity: sha1-hOti6Hb+lJrh/WLGCARCPbwsRHI=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/otplease': 4.0.0
      '@lerna/run-lifecycle': 4.0.0
      fs-extra: 9.1.0
      libnpmpublish: 4.0.2
      npm-package-arg: 8.1.5
      npmlog: 4.1.2
      pify: 5.0.0
      read-package-json: 3.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /@lerna/npm-run-script/4.0.0:
    resolution: {integrity: sha1-3+v09GAUQufAtSFPn7DZbJNQdDs=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      '@lerna/get-npm-exec-opts': 4.0.0
      npmlog: 4.1.2
    dev: true

  /@lerna/otplease/4.0.0:
    resolution: {integrity: sha1-hJcutDRI+KEHdDW6HF5ZIztyWFA=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/prompt': 4.0.0
    dev: true

  /@lerna/output/4.0.0:
    resolution: {integrity: sha1-sdciFcDjVIPk8+mZTevILGIYUfI=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      npmlog: 4.1.2
    dev: true

  /@lerna/pack-directory/4.0.0:
    resolution: {integrity: sha1-i2F9uV0geS8EOqqhOpzMDgTLTHQ=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/get-packed': 4.0.0
      '@lerna/package': 4.0.0
      '@lerna/run-lifecycle': 4.0.0
      npm-packlist: 2.2.2
      npmlog: 4.1.2
      tar: 6.2.1
      temp-write: 4.0.0
    dev: true

  /@lerna/package-graph/4.0.0:
    resolution: {integrity: sha1-FqACU6isgQ9yBBSBy0a87o2BI90=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/prerelease-id-from-version': 4.0.0
      '@lerna/validation-error': 4.0.0
      npm-package-arg: 8.1.5
      npmlog: 4.1.2
      semver: 7.7.2
    dev: true

  /@lerna/package/4.0.0:
    resolution: {integrity: sha1-G0wlnEvP9FyHbuHVkaBDqsvA1rc=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      load-json-file: 6.2.0
      npm-package-arg: 8.1.5
      write-pkg: 4.0.0
    dev: true

  /@lerna/prerelease-id-from-version/4.0.0:
    resolution: {integrity: sha1-x+Bnb87hlQ2FYw4Qjt3s3VtIyRY=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      semver: 7.7.2
    dev: true

  /@lerna/profiler/4.0.0:
    resolution: {integrity: sha1-ilOrh0Ui6uFdF4QCv/kKFAcZCOk=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      fs-extra: 9.1.0
      npmlog: 4.1.2
      upath: 2.0.1
    dev: true

  /@lerna/project/4.0.0:
    resolution: {integrity: sha1-/4SJOTWDNTOnTe/zDA5k3bfwums=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/package': 4.0.0
      '@lerna/validation-error': 4.0.0
      cosmiconfig: 7.1.0
      dedent: 0.7.0
      dot-prop: 6.0.1
      glob-parent: 5.1.2
      globby: 11.1.0
      load-json-file: 6.2.0
      npmlog: 4.1.2
      p-map: 4.0.0
      resolve-from: 5.0.0
      write-json-file: 4.3.0
    dev: true

  /@lerna/prompt/4.0.0:
    resolution: {integrity: sha1-XsaagD8/DbCtnyIdrWRmTT2spBs=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      inquirer: 7.3.3
      npmlog: 4.1.2
    dev: true

  /@lerna/publish/4.0.0:
    resolution: {integrity: sha1-9nARMFreuhIAZqO22YSlu1/O72U=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/check-working-tree': 4.0.0
      '@lerna/child-process': 4.0.0
      '@lerna/collect-updates': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/describe-ref': 4.0.0
      '@lerna/log-packed': 4.0.0
      '@lerna/npm-conf': 4.0.0
      '@lerna/npm-dist-tag': 4.0.0
      '@lerna/npm-publish': 4.0.0
      '@lerna/otplease': 4.0.0
      '@lerna/output': 4.0.0
      '@lerna/pack-directory': 4.0.0
      '@lerna/prerelease-id-from-version': 4.0.0
      '@lerna/prompt': 4.0.0
      '@lerna/pulse-till-done': 4.0.0
      '@lerna/run-lifecycle': 4.0.0
      '@lerna/run-topologically': 4.0.0
      '@lerna/validation-error': 4.0.0
      '@lerna/version': 4.0.0
      fs-extra: 9.1.0
      libnpmaccess: 4.0.3
      npm-package-arg: 8.1.5
      npm-registry-fetch: 9.0.0
      npmlog: 4.1.2
      p-map: 4.0.0
      p-pipe: 3.1.0
      pacote: 11.3.5
      semver: 7.7.2
    transitivePeerDependencies:
      - bluebird
      - encoding
      - supports-color
    dev: true

  /@lerna/pulse-till-done/4.0.0:
    resolution: {integrity: sha1-BLrOfUg6ggXBh7gGvNi+I9e7gKM=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      npmlog: 4.1.2
    dev: true

  /@lerna/query-graph/4.0.0:
    resolution: {integrity: sha1-Cd0cgZrF7j842yOTEUNwH4pu72M=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/package-graph': 4.0.0
    dev: true

  /@lerna/resolve-symlink/4.0.0:
    resolution: {integrity: sha1-bQBmKKIQybghlkZXqeIKjJoRXhQ=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      fs-extra: 9.1.0
      npmlog: 4.1.2
      read-cmd-shim: 2.0.0
    dev: true

  /@lerna/rimraf-dir/4.0.0:
    resolution: {integrity: sha1-Lt87YtTrDvTkTkMPWERmfVUewlo=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/child-process': 4.0.0
      npmlog: 4.1.2
      path-exists: 4.0.0
      rimraf: 3.0.2
    dev: true

  /@lerna/run-lifecycle/4.0.0:
    resolution: {integrity: sha1-5kikb5IQqbzXw5HfaERJjLUHkzQ=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/npm-conf': 4.0.0
      npm-lifecycle: 3.1.5
      npmlog: 4.1.2
    dev: true

  /@lerna/run-topologically/4.0.0:
    resolution: {integrity: sha1-r4Ru7uGgmwwr4NG/te8PewS7GCc=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/query-graph': 4.0.0
      p-queue: 6.6.2
    dev: true

  /@lerna/run/4.0.0:
    resolution: {integrity: sha1-S8f9oFWnKUh4l8I1eWlPYYPJEmI=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/command': 4.0.0
      '@lerna/filter-options': 4.0.0
      '@lerna/npm-run-script': 4.0.0
      '@lerna/output': 4.0.0
      '@lerna/profiler': 4.0.0
      '@lerna/run-topologically': 4.0.0
      '@lerna/timer': 4.0.0
      '@lerna/validation-error': 4.0.0
      p-map: 4.0.0
    dev: true

  /@lerna/symlink-binary/4.0.0:
    resolution: {integrity: sha1-IQCfYtU6Ql8TbLTBoyxrKgzALUc=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/create-symlink': 4.0.0
      '@lerna/package': 4.0.0
      fs-extra: 9.1.0
      p-map: 4.0.0
    dev: true

  /@lerna/symlink-dependencies/4.0.0:
    resolution: {integrity: sha1-iRDsoISuBiZC0EkNiXLPLZjp670=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/create-symlink': 4.0.0
      '@lerna/resolve-symlink': 4.0.0
      '@lerna/symlink-binary': 4.0.0
      fs-extra: 9.1.0
      p-map: 4.0.0
      p-map-series: 2.1.0
    dev: true

  /@lerna/timer/4.0.0:
    resolution: {integrity: sha1-pS5Rv805v9domIBJrOexXB/Xpto=}
    engines: {node: '>= 10.18.0'}
    dev: true

  /@lerna/validation-error/4.0.0:
    resolution: {integrity: sha1-r51i/oME6qLrmmuhOU+aqAcCbTU=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      npmlog: 4.1.2
    dev: true

  /@lerna/version/4.0.0:
    resolution: {integrity: sha1-UyZZ7GFU2Kh4nFq1OHhmPiROMig=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      '@lerna/check-working-tree': 4.0.0
      '@lerna/child-process': 4.0.0
      '@lerna/collect-updates': 4.0.0
      '@lerna/command': 4.0.0
      '@lerna/conventional-commits': 4.0.0
      '@lerna/github-client': 4.0.0
      '@lerna/gitlab-client': 4.0.0
      '@lerna/output': 4.0.0
      '@lerna/prerelease-id-from-version': 4.0.0
      '@lerna/prompt': 4.0.0
      '@lerna/run-lifecycle': 4.0.0
      '@lerna/run-topologically': 4.0.0
      '@lerna/validation-error': 4.0.0
      chalk: 4.1.2
      dedent: 0.7.0
      load-json-file: 6.2.0
      minimatch: 3.1.2
      npmlog: 4.1.2
      p-map: 4.0.0
      p-pipe: 3.1.0
      p-reduce: 2.1.0
      p-waterfall: 2.1.1
      semver: 7.7.2
      slash: 3.0.0
      temp-write: 4.0.0
      write-json-file: 4.3.0
    transitivePeerDependencies:
      - encoding
    dev: true

  /@lerna/write-log-file/4.0.0:
    resolution: {integrity: sha1-GCIaOKajB9awpYRN1ZKtU/onCR4=}
    engines: {node: '>= 10.18.0'}
    dependencies:
      npmlog: 4.1.2
      write-file-atomic: 3.0.3
    dev: true

  /@node-redis/client/1.0.6:
    resolution: {integrity: sha512-SRlHlrz2xAg1o5j9ZdnoF2fA8EZydbXr1mdUv35w0O0NP3XcYt3nQ7WAtmOu13kUa90PhmVoqTDMsTereAETfg==}
    engines: {node: '>=12'}
    dependencies:
      cluster-key-slot: 1.1.0
      generic-pool: 3.8.2
      redis-parser: 3.0.0
      yallist: 4.0.0
    dev: true

  /@node-redis/json/1.0.2_@node-redis+client@1.0.6:
    resolution: {integrity: sha512-qVRgn8WfG46QQ08CghSbY4VhHFgaTY71WjpwRBGEuqGPfWwfRcIf3OqSpR7Q/45X+v3xd8mvYjywqh0wqJ8T+g==}
    peerDependencies:
      '@node-redis/client': ^1.0.0
    dependencies:
      '@node-redis/client': 1.0.6
    dev: true

  /@node-redis/search/1.0.5_@node-redis+client@1.0.6:
    resolution: {integrity: sha512-MCOL8iCKq4v+3HgEQv8zGlSkZyXSXtERgrAJ4TSryIG/eLFy84b57KmNNa/V7M1Q2Wd2hgn2nPCGNcQtk1R1OQ==}
    peerDependencies:
      '@node-redis/client': ^1.0.0
    dependencies:
      '@node-redis/client': 1.0.6
    dev: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1
    dev: true

  /@npmcli/ci-detect/1.4.0:
    resolution: {integrity: sha512-3BGrt6FLjqM6br5AhWRKTr3u5GIVkjRYeAFrMp3HjnfICrg4xOrVRwFavKT6tsp++bq5dluL5t8ME/Nha/6c1Q==}
    dev: true

  /@npmcli/fs/1.1.1:
    resolution: {integrity: sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==}
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.7.2
    dev: true

  /@npmcli/git/2.1.0:
    resolution: {integrity: sha1-L7134UdTAkfTfzJZMNRXs+volPY=}
    dependencies:
      '@npmcli/promise-spawn': 1.3.2
      lru-cache: 6.0.0
      mkdirp: 1.0.4
      npm-pick-manifest: 6.1.1
      promise-inflight: 1.0.1
      promise-retry: 2.0.1
      semver: 7.7.2
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
    dev: true

  /@npmcli/installed-package-contents/1.0.7:
    resolution: {integrity: sha1-q3QIxhR5EblwqKviYc5RIjKj9Po=}
    engines: {node: '>= 10'}
    hasBin: true
    dependencies:
      npm-bundled: 1.1.2
      npm-normalize-package-bin: 1.0.1
    dev: true

  /@npmcli/move-file/1.1.2:
    resolution: {integrity: sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ=}
    engines: {node: '>=10'}
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    dev: true

  /@npmcli/node-gyp/1.0.3:
    resolution: {integrity: sha512-fnkhw+fmX65kiLqk6E3BFLXNC26rUhK90zVwe2yncPliVT/Qos3xjhTLE59Df8KnPlcwIERXKVlU1bXoUQ+liA==}
    dev: true

  /@npmcli/promise-spawn/1.3.2:
    resolution: {integrity: sha1-QtTlao6SdPuhgNq8CupuOPKSdPU=}
    dependencies:
      infer-owner: 1.0.4
    dev: true

  /@npmcli/run-script/1.8.6:
    resolution: {integrity: sha1-GDFIAqZmCw1Lqkw6/n8a052MKLc=}
    dependencies:
      '@npmcli/node-gyp': 1.0.3
      '@npmcli/promise-spawn': 1.3.2
      node-gyp: 7.1.2
      read-package-json-fast: 2.0.3
    dev: true

  /@octokit/auth-token/2.5.0:
    resolution: {integrity: sha512-r5FVUJCOLl19AxiuZD2VRZ/ORjp/4IN98Of6YJoJOkY75CIBuYfmiNHGrDwXr+aLGG55igl9QrxX3hbiXlLb+g==}
    dependencies:
      '@octokit/types': 6.41.0
    dev: true

  /@octokit/core/3.6.0:
    resolution: {integrity: sha512-7RKRKuA4xTjMhY+eG3jthb3hlZCsOwg3rztWh75Xc+ShDWOfDDATWbeZpAHBNRpm4Tv9WgBMOy1zEJYXG6NJ7Q==}
    dependencies:
      '@octokit/auth-token': 2.5.0
      '@octokit/graphql': 4.8.0
      '@octokit/request': 5.6.3
      '@octokit/request-error': 2.1.0
      '@octokit/types': 6.41.0
      before-after-hook: 2.2.3
      universal-user-agent: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/endpoint/6.0.12:
    resolution: {integrity: sha1-O01HpLDnmxAn+4111CIZKLLQVlg=}
    dependencies:
      '@octokit/types': 6.41.0
      is-plain-object: 5.0.0
      universal-user-agent: 6.0.1
    dev: true

  /@octokit/graphql/4.8.0:
    resolution: {integrity: sha1-Zk2bEcDhIRLL944Q9JoFlZqiLMM=}
    dependencies:
      '@octokit/request': 5.6.3
      '@octokit/types': 6.41.0
      universal-user-agent: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/openapi-types/12.11.0:
    resolution: {integrity: sha512-VsXyi8peyRq9PqIz/tpqiL2w3w80OgVMwBHltTml3LmVvXiphgeqmY9mvBw9Wu7e0QWk/fqD37ux8yP5uVekyQ==}
    dev: true

  /@octokit/plugin-enterprise-rest/6.0.1:
    resolution: {integrity: sha1-4HiWc5YY2rjafUB3xlgAN3X5VDc=}
    dev: true

  /@octokit/plugin-paginate-rest/2.21.3_@octokit+core@3.6.0:
    resolution: {integrity: sha512-aCZTEf0y2h3OLbrgKkrfFdjRL6eSOo8komneVQJnYecAxIej7Bafor2xhuDJOIFau4pk0i/P28/XgtbyPF0ZHw==}
    peerDependencies:
      '@octokit/core': '>=2'
    dependencies:
      '@octokit/core': 3.6.0
      '@octokit/types': 6.41.0
    dev: true

  /@octokit/plugin-request-log/1.0.4_@octokit+core@3.6.0:
    resolution: {integrity: sha1-XlDtcIOmE4FrHkooruxft/FGLoU=}
    peerDependencies:
      '@octokit/core': '>=3'
    dependencies:
      '@octokit/core': 3.6.0
    dev: true

  /@octokit/plugin-rest-endpoint-methods/5.16.2_@octokit+core@3.6.0:
    resolution: {integrity: sha512-8QFz29Fg5jDuTPXVtey05BLm7OB+M8fnvE64RNegzX7U+5NUXcOcnpTIK0YfSHBg8gYd0oxIq3IZTe9SfPZiRw==}
    peerDependencies:
      '@octokit/core': '>=3'
    dependencies:
      '@octokit/core': 3.6.0
      '@octokit/types': 6.41.0
      deprecation: 2.3.1
    dev: true

  /@octokit/request-error/2.1.0:
    resolution: {integrity: sha1-nhUDV4Mb/HiNE6T9SxkT1gx01nc=}
    dependencies:
      '@octokit/types': 6.41.0
      deprecation: 2.3.1
      once: 1.4.0
    dev: true

  /@octokit/request/5.6.3:
    resolution: {integrity: sha512-bFJl0I1KVc9jYTe9tdGGpAMPy32dLBXXo1dS/YwSCTL/2nd9XeHsY616RE3HPXDVk+a+dBuzyz5YdlXwcDTr2A==}
    dependencies:
      '@octokit/endpoint': 6.0.12
      '@octokit/request-error': 2.1.0
      '@octokit/types': 6.41.0
      is-plain-object: 5.0.0
      node-fetch: 2.7.0
      universal-user-agent: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/rest/18.12.0:
    resolution: {integrity: sha512-gDPiOHlyGavxr72y0guQEhLsemgVjwRePayJ+FcKc2SJqKUbxbkvf5kAZEWA/MKvsfYlQAMVzNJE3ezQcxMJ2Q==}
    dependencies:
      '@octokit/core': 3.6.0
      '@octokit/plugin-paginate-rest': 2.21.3_@octokit+core@3.6.0
      '@octokit/plugin-request-log': 1.0.4_@octokit+core@3.6.0
      '@octokit/plugin-rest-endpoint-methods': 5.16.2_@octokit+core@3.6.0
    transitivePeerDependencies:
      - encoding
    dev: true

  /@octokit/types/6.41.0:
    resolution: {integrity: sha512-eJ2jbzjdijiL3B4PrSQaSjuF2sPEQPVCPzBvTHJD9Nz+9dw2SGH4K4xeQJ77YfTq5bRQ+bD8wT11JbeDPmxmGg==}
    dependencies:
      '@octokit/openapi-types': 12.11.0
    dev: true

  /@rtsao/scc/1.1.0:
    resolution: {integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==}
    dev: true

  /@samverschueren/stream-to-observable/0.3.1_rxjs@6.6.7:
    resolution: {integrity: sha1-ohEXsZ7pvnDDeewYd1N+8uHGMwE=}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zen-observable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zen-observable:
        optional: true
    dependencies:
      any-observable: 0.3.0_rxjs@6.6.7
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zenObservable
    dev: true

  /@sindresorhus/is/4.6.0:
    resolution: {integrity: sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==}
    engines: {node: '>=10'}
    dev: true

  /@szmarczak/http-timer/4.0.6:
    resolution: {integrity: sha1-tKkUu2LnwnLU5Zif5EQPgSqx2Ac=}
    engines: {node: '>=10'}
    dependencies:
      defer-to-connect: 2.0.1
    dev: true

  /@tootallnate/once/1.1.2:
    resolution: {integrity: sha1-zLkURTYBeaBOf+av94wA/8Hur4I=}
    engines: {node: '>= 6'}
    dev: true

  /@types/body-parser/1.19.6:
    resolution: {integrity: sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==}
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 22.16.4

  /@types/bonjour/3.5.13:
    resolution: {integrity: sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ==}
    dependencies:
      '@types/node': 22.16.4

  /@types/cacheable-request/6.0.3:
    resolution: {integrity: sha512-IQ3EbTzGxIigb1I3qPZc1rWJnH0BmSKv5QYTalEwweFvyBDLSAe24zP0le/hyi7ecGfZVlIVAg4BZqb8WBwKqw==}
    dependencies:
      '@types/http-cache-semantics': 4.0.4
      '@types/keyv': 3.1.4
      '@types/node': 22.16.4
      '@types/responselike': 1.0.3
    dev: true

  /@types/connect-history-api-fallback/1.5.4:
    resolution: {integrity: sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw==}
    dependencies:
      '@types/express-serve-static-core': 5.0.7
      '@types/node': 22.16.4

  /@types/connect/3.4.38:
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}
    dependencies:
      '@types/node': 22.16.4

  /@types/crypto-js/4.2.2:
    resolution: {integrity: sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==}
    dev: true

  /@types/eslint-scope/3.7.7:
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.8

  /@types/eslint-visitor-keys/1.0.0:
    resolution: {integrity: sha512-OCutwjDZ4aFS6PB1UZ988C4YgwlBHJd6wCeQqaLdmadZ/7e+w79+hbMUFC1QXDNCmdyoRfAFdm0RypzwR+Qpag==}
    dev: true

  /@types/eslint/9.6.1:
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==}
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15

  /@types/estree/1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  /@types/express-serve-static-core/4.19.6:
    resolution: {integrity: sha512-N4LZ2xG7DatVqhCZzOGb1Yi5lMbXSZcmdLDe9EzSndPV2HpWYWzRbaerl2n27irrm94EPpprqa8KpskPT085+A==}
    dependencies:
      '@types/node': 22.16.4
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.5

  /@types/express-serve-static-core/5.0.7:
    resolution: {integrity: sha512-R+33OsgWw7rOhD1emjU7dzCDHucJrgJXMA5PYCzJxVil0dsyx5iBEPHqpPfiKNJQb7lZ1vxwoLR4Z87bBUpeGQ==}
    dependencies:
      '@types/node': 22.16.4
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.5

  /@types/express/4.17.23:
    resolution: {integrity: sha512-Crp6WY9aTYP3qPi2wGDo9iUe/rceX01UMhnF1jmwDcKCFM6cx7YhGP/Mpr3y9AASpfHixIG0E6azCcL5OcDHsQ==}
    dependencies:
      '@types/body-parser': 1.19.6
      '@types/express-serve-static-core': 4.19.6
      '@types/qs': 6.14.0
      '@types/serve-static': 1.15.8

  /@types/fined/1.1.5:
    resolution: {integrity: sha512-2N93vadEGDFhASTIRbizbl4bNqpMOId5zZfj6hHqYZfEzEfO9onnU4Im8xvzo8uudySDveDHBOOSlTWf38ErfQ==}
    dev: true

  /@types/glob/7.2.0:
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=}
    dependencies:
      '@types/minimatch': 6.0.0
      '@types/node': 22.16.4
    dev: true

  /@types/google-libphonenumber/7.4.30:
    resolution: {integrity: sha512-Td1X1ayRxePEm6/jPHUBs2tT6TzW1lrVB6ZX7ViPGellyzO/0xMNi+wx5nH6jEitjznq276VGIqjK5qAju0XVw==}
    dev: true

  /@types/highlight.js/9.12.4:
    resolution: {integrity: sha1-jDSWvRtQzASu79aRFAqlcdTb+jQ=}
    dev: true

  /@types/html-minifier-terser/6.1.0:
    resolution: {integrity: sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==}
    dev: true

  /@types/http-cache-semantics/4.0.4:
    resolution: {integrity: sha512-1m0bIFVc7eJWyve9S0RnuRgcQqF/Xd5QsUZAZeQFr1Q3/p9JWoQQEqmVy+DPTNpGXwhgIetAoYF8JSc33q29QA==}
    dev: true

  /@types/http-errors/2.0.5:
    resolution: {integrity: sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==}

  /@types/http-proxy/1.17.16:
    resolution: {integrity: sha512-sdWoUajOB1cd0A8cRRQ1cfyWNbmFKLAqBB89Y8x5iYyG/mkJHc0YUH8pdWBy2omi9qtCpiIgGjuwO0dQST2l5w==}
    dependencies:
      '@types/node': 22.16.4

  /@types/inquirer/9.0.8:
    resolution: {integrity: sha512-CgPD5kFGWsb8HJ5K7rfWlifao87m4ph8uioU7OTncJevmE/VLIqAAjfQtko578JZg7/f69K4FgqYym3gNr7DeA==}
    dependencies:
      '@types/through': 0.0.33
      rxjs: 7.8.2
    dev: true

  /@types/json-schema/7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  /@types/json5/0.0.29:
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=}
    dev: true

  /@types/keyv/3.1.4:
    resolution: {integrity: sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==}
    dependencies:
      '@types/node': 22.16.4
    dev: true

  /@types/liftoff/4.0.3:
    resolution: {integrity: sha512-UgbL2kR5pLrWICvr8+fuSg0u43LY250q7ZMkC+XKC3E+rs/YBDEnQIzsnhU5dYsLlwMi3R75UvCL87pObP1sxw==}
    dependencies:
      '@types/fined': 1.1.5
      '@types/node': 22.16.4
    dev: true

  /@types/linkify-it/5.0.0:
    resolution: {integrity: sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==}
    dev: true

  /@types/lodash/4.17.20:
    resolution: {integrity: sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==}
    dev: true

  /@types/markdown-it/10.0.3:
    resolution: {integrity: sha1-qYANFLESwX8d527DPv+GSkgV7sc=}
    dependencies:
      '@types/highlight.js': 9.12.4
      '@types/linkify-it': 5.0.0
      '@types/mdurl': 2.0.0
      highlight.js: 9.18.4
    dev: true

  /@types/mdurl/2.0.0:
    resolution: {integrity: sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==}
    dev: true

  /@types/mime/1.3.5:
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==}

  /@types/minimatch/3.0.5:
    resolution: {integrity: sha1-EAHMXmo3BLg8I2An538vWOoBD0A=}
    dev: true

  /@types/minimatch/6.0.0:
    resolution: {integrity: sha512-zmPitbQ8+6zNutpwgcQuLcsEpn/Cj54Kbn7L5pX0Os5kdWplB7xPgEh/g+SWOB/qmows2gpuCaPyduq8ZZRnxA==}
    deprecated: This is a stub types definition. minimatch provides its own type definitions, so you do not need this installed.
    dependencies:
      minimatch: 10.0.3
    dev: true

  /@types/minimist/1.2.5:
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==}
    dev: true

  /@types/node-forge/1.3.13:
    resolution: {integrity: sha512-zePQJSW5QkwSHKRApqWCVKeKoSOt4xvEnLENZPjyvm9Ezdf/EyDeJM7jqLzOwjVICQQzvLZ63T55MKdJB5H6ww==}
    dependencies:
      '@types/node': 22.16.4

  /@types/node/14.18.63:
    resolution: {integrity: sha512-fAtCfv4jJg+ExtXhvCkCqUKZ+4ok/JQk01qDKhL5BDDoS3AxKXhV5/MAVUZyQnSEd2GT92fkgZl0pz0Q0AzcIQ==}
    dev: true

  /@types/node/22.16.4:
    resolution: {integrity: sha512-PYRhNtZdm2wH/NT2k/oAJ6/f2VD2N2Dag0lGlx2vWgMSJXGNmlce5MiTQzoWAiIJtso30mjnfQCOKVH+kAQC/g==}
    dependencies:
      undici-types: 6.21.0

  /@types/normalize-package-data/2.4.4:
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==}
    dev: true

  /@types/parse-json/4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}
    dev: true

  /@types/prop-types/15.7.15:
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==}
    dev: true

  /@types/qs/6.14.0:
    resolution: {integrity: sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==}

  /@types/range-parser/1.2.7:
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==}

  /@types/react-reconciler/0.18.0:
    resolution: {integrity: sha1-/OK0RBA/RJBOc+ujSaxlUuJ3H2Q=}
    dependencies:
      '@types/react': 16.14.65
    dev: true

  /@types/react/16.14.65:
    resolution: {integrity: sha512-Guc3kE+W8LrQB9I3bF3blvNH15dXFIVIHIJTqrF8cp5XI/3IJcHGo4C3sJNPb8Zx49aofXKnAGIKyonE4f7XWg==}
    dependencies:
      '@types/prop-types': 15.7.15
      '@types/scheduler': 0.16.8
      csstype: 3.1.3
    dev: true

  /@types/responselike/1.0.3:
    resolution: {integrity: sha512-H/+L+UkTV33uf49PH5pCAUBVPNj2nDBXTN+qS1dOwyyg24l3CcicicCA7ca+HMvJBZcFgl5r8e+RR6elsb4Lyw==}
    dependencies:
      '@types/node': 22.16.4
    dev: true

  /@types/retry/0.12.0:
    resolution: {integrity: sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=}

  /@types/scheduler/0.16.8:
    resolution: {integrity: sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==}
    dev: true

  /@types/send/0.17.5:
    resolution: {integrity: sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==}
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 22.16.4

  /@types/serve-index/1.9.4:
    resolution: {integrity: sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug==}
    dependencies:
      '@types/express': 4.17.23

  /@types/serve-static/1.15.8:
    resolution: {integrity: sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==}
    dependencies:
      '@types/http-errors': 2.0.5
      '@types/node': 22.16.4
      '@types/send': 0.17.5

  /@types/sockjs/0.3.36:
    resolution: {integrity: sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q==}
    dependencies:
      '@types/node': 22.16.4

  /@types/through/0.0.33:
    resolution: {integrity: sha512-HsJ+z3QuETzP3cswwtzt2vEIiHBk/dCcHGhbmG5X3ecnwFD/lPrMpliGXxSCg03L9AhrdwA4Oz/qfspkDW+xGQ==}
    dependencies:
      '@types/node': 22.16.4
    dev: true

  /@types/triple-beam/1.3.5:
    resolution: {integrity: sha512-6WaYesThRMCl19iryMYP7/x2OVgCtbIVflDGFpWnb9irXI3UjYE4AzmYuiUKY1AJstGijoY+MgUszMgRxIYTYw==}
    dev: true

  /@types/ws/8.18.1:
    resolution: {integrity: sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==}
    dependencies:
      '@types/node': 22.16.4

  /@typescript-eslint/eslint-plugin/3.10.1_kxujzhw6vbtdri44htsqryf25e:
    resolution: {integrity: sha1-fgYTOKE4P1ntwgTGBYmfk9wuLI8=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^3.0.0
      eslint: ^5.0.0 || ^6.0.0 || ^7.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/experimental-utils': 3.10.1_2de3j2mqba4wgeuiaqz2k7syrm
      '@typescript-eslint/parser': 3.10.1_2de3j2mqba4wgeuiaqz2k7syrm
      debug: 4.4.1
      eslint: 7.32.0
      functional-red-black-tree: 1.0.1
      regexpp: 3.2.0
      semver: 7.7.2
      tsutils: 3.21.0_typescript@3.9.10
      typescript: 3.9.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/eslint-plugin/3.10.1_rn5bylxdotrtoiyaug6uygcpa4:
    resolution: {integrity: sha1-fgYTOKE4P1ntwgTGBYmfk9wuLI8=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^3.0.0
      eslint: ^5.0.0 || ^6.0.0 || ^7.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/experimental-utils': 3.10.1_jofidmxrjzhj7l6vknpw5ecvfe
      '@typescript-eslint/parser': 3.10.1_jofidmxrjzhj7l6vknpw5ecvfe
      debug: 4.4.1
      eslint: 7.32.0
      functional-red-black-tree: 1.0.1
      regexpp: 3.2.0
      semver: 7.7.2
      tsutils: 3.21.0_typescript@4.9.5
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/experimental-utils/3.10.1_2de3j2mqba4wgeuiaqz2k7syrm:
    resolution: {integrity: sha1-4Xn/yBqA68ri6gTgMy+LJRNFpoY=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: '*'
    dependencies:
      '@types/json-schema': 7.0.15
      '@typescript-eslint/types': 3.10.1
      '@typescript-eslint/typescript-estree': 3.10.1_typescript@3.9.10
      eslint: 7.32.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /@typescript-eslint/experimental-utils/3.10.1_jofidmxrjzhj7l6vknpw5ecvfe:
    resolution: {integrity: sha1-4Xn/yBqA68ri6gTgMy+LJRNFpoY=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: '*'
    dependencies:
      '@types/json-schema': 7.0.15
      '@typescript-eslint/types': 3.10.1
      '@typescript-eslint/typescript-estree': 3.10.1_typescript@4.9.5
      eslint: 7.32.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /@typescript-eslint/parser/3.10.1_2de3j2mqba4wgeuiaqz2k7syrm:
    resolution: {integrity: sha1-GIOFjoPotEJifhrG9AiSUhEVVGc=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0 || ^7.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@types/eslint-visitor-keys': 1.0.0
      '@typescript-eslint/experimental-utils': 3.10.1_2de3j2mqba4wgeuiaqz2k7syrm
      '@typescript-eslint/types': 3.10.1
      '@typescript-eslint/typescript-estree': 3.10.1_typescript@3.9.10
      eslint: 7.32.0
      eslint-visitor-keys: 1.3.0
      typescript: 3.9.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/parser/3.10.1_jofidmxrjzhj7l6vknpw5ecvfe:
    resolution: {integrity: sha1-GIOFjoPotEJifhrG9AiSUhEVVGc=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0 || ^7.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@types/eslint-visitor-keys': 1.0.0
      '@typescript-eslint/experimental-utils': 3.10.1_jofidmxrjzhj7l6vknpw5ecvfe
      '@typescript-eslint/types': 3.10.1
      '@typescript-eslint/typescript-estree': 3.10.1_typescript@4.9.5
      eslint: 7.32.0
      eslint-visitor-keys: 1.3.0
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/types/3.10.1:
    resolution: {integrity: sha1-HXRj+nwy2KI6tQioA8ov4m51hyc=}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    dev: true

  /@typescript-eslint/typescript-estree/3.10.1_typescript@3.9.10:
    resolution: {integrity: sha1-/QBhzDit1PrUUTbWVECFafNluFM=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 3.10.1
      '@typescript-eslint/visitor-keys': 3.10.1
      debug: 4.4.1
      glob: 7.2.3
      is-glob: 4.0.3
      lodash: 4.17.21
      semver: 7.7.2
      tsutils: 3.21.0_typescript@3.9.10
      typescript: 3.9.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/typescript-estree/3.10.1_typescript@4.9.5:
    resolution: {integrity: sha1-/QBhzDit1PrUUTbWVECFafNluFM=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 3.10.1
      '@typescript-eslint/visitor-keys': 3.10.1
      debug: 4.4.1
      glob: 7.2.3
      is-glob: 4.0.3
      lodash: 4.17.21
      semver: 7.7.2
      tsutils: 3.21.0_typescript@4.9.5
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/visitor-keys/3.10.1:
    resolution: {integrity: sha1-zUJ0dz4+tjsuhwrGAidEh+zR6TE=}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /@webassemblyjs/ast/1.14.1:
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==}
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==}

  /@webassemblyjs/helper-api-error/1.13.2:
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==}

  /@webassemblyjs/helper-buffer/1.14.1:
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==}

  /@webassemblyjs/helper-numbers/1.13.2:
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==}
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==}

  /@webassemblyjs/helper-wasm-section/1.14.1:
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  /@webassemblyjs/ieee754/1.13.2:
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==}
    dependencies:
      '@xtuc/ieee754': 1.2.0

  /@webassemblyjs/leb128/1.13.2:
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==}
    dependencies:
      '@xtuc/long': 4.2.2

  /@webassemblyjs/utf8/1.13.2:
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==}

  /@webassemblyjs/wasm-edit/1.14.1:
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  /@webassemblyjs/wasm-gen/1.14.1:
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  /@webassemblyjs/wasm-opt/1.14.1:
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  /@webassemblyjs/wasm-parser/1.14.1:
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  /@webassemblyjs/wast-printer/1.14.1:
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  /@webpack-cli/configtest/2.1.1_goe534c4oany46n3ybfcwdca2e:
    resolution: {integrity: sha512-wy0mglZpDSiSS0XHrVR+BAdId2+yxPSoJW8fsna3ZpYSlufjvxnP4YbKTCBZnNIcGN4r6ZPXV55X4mYExOfLmw==}
    engines: {node: '>=14.15.0'}
    peerDependencies:
      webpack: 5.x.x
      webpack-cli: 5.x.x
    dependencies:
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-cli: 5.1.4_p2e7gummt6gr4psp4bot4sod7m

  /@webpack-cli/info/2.0.2_goe534c4oany46n3ybfcwdca2e:
    resolution: {integrity: sha512-zLHQdI/Qs1UyT5UBdWNqsARasIA+AaF8t+4u2aS2nEpBQh2mWIVb8qAklq0eUENnC5mOItrIB4LiS9xMtph18A==}
    engines: {node: '>=14.15.0'}
    peerDependencies:
      webpack: 5.x.x
      webpack-cli: 5.x.x
    dependencies:
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-cli: 5.1.4_p2e7gummt6gr4psp4bot4sod7m

  /@webpack-cli/serve/2.0.5_qyiwwxvrjnka5zdd7c3rrfofzu:
    resolution: {integrity: sha512-lqaoKnRYBdo1UgDX8uF24AfGMifWK19TxPmM5FHc2vAGxrJ/qtyUyFBWoY1tISZdelsQ5fBcOusifo5o5wSJxQ==}
    engines: {node: '>=14.15.0'}
    peerDependencies:
      webpack: 5.x.x
      webpack-cli: 5.x.x
      webpack-dev-server: '*'
    peerDependenciesMeta:
      webpack-dev-server:
        optional: true
    dependencies:
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-cli: 5.1.4_p2e7gummt6gr4psp4bot4sod7m
      webpack-dev-server: 4.15.2_goe534c4oany46n3ybfcwdca2e

  /@xtuc/ieee754/1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  /@xtuc/long/4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  /JSONStream/1.3.5:
    resolution: {integrity: sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=}
    hasBin: true
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8
    dev: true

  /abbrev/1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}
    dev: true

  /abc-fed-build-tool/0.7.9_co@4.6.0+webpack@5.100.1:
    resolution: {integrity: sha512-MRHLdxLRJF7iMcBHaBXCvtRQRWJNZmcV9kYr0jX4bYOgjibNQp3KJfcVWjMbxQFCZJYJFiozYTKpFYNlPDblvA==}
    dependencies:
      '@babel/core': 7.24.4
      '@babel/parser': 7.24.4
      '@babel/types': 7.24.0
      ali-oss: 6.16.0_co@4.6.0
      archiver: 6.0.2
      asar: 3.2.0
      chalk: 4.1.2
      cos-nodejs-sdk-v5: 2.11.4
      external-remotes-plugin: 1.0.0_webpack@5.100.1
      glob: 8.0.3
      got: 11.8.6
      listr: 0.14.3
      lodash: 4.17.21
      uglify-js: 3.17.4
    transitivePeerDependencies:
      - co
      - proxy-agent
      - supports-color
      - webpack
      - zen-observable
      - zenObservable
    dev: true

  /accepts/1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  /acorn-import-phases/1.0.4_acorn@8.15.0:
    resolution: {integrity: sha512-wKmbr/DDiIXzEOiWrTTUcDm24kQ2vGfZQvM2fwg2vXqR5uW6aapr7ObPtj1th32b9u90/Pf4AItvdTh42fBmVQ==}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      acorn: ^8.14.0
    dependencies:
      acorn: 8.15.0

  /acorn-jsx/5.3.2_acorn@7.4.1:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 7.4.1
    dev: true

  /acorn/7.4.1:
    resolution: {integrity: sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /acorn/8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /add-stream/1.0.0:
    resolution: {integrity: sha1-anmQQ3ynNtXhKI25K9MmbV9csqo=}
    dev: true

  /address/1.2.2:
    resolution: {integrity: sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /agent-base/6.0.2:
    resolution: {integrity: sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=}
    engines: {node: '>= 6.0.0'}
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /agentkeepalive/3.5.3:
    resolution: {integrity: sha512-yqXL+k5rr8+ZRpOAntkaaRgWgE5o8ESAj5DyRmVTCSoZxXmqemb9Dd7T4i5UzwuERdLAJUy6XzR9zFVuf0kzkw==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      humanize-ms: 1.2.1
    dev: true

  /agentkeepalive/4.6.0:
    resolution: {integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      humanize-ms: 1.2.1
    dev: true

  /aggregate-error/3.1.0:
    resolution: {integrity: sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /aggregate-error/4.0.1:
    resolution: {integrity: sha512-0poP0T7el6Vq3rstR8Mn4V/IQrpBLO6POkUSrN7RhyY+GF/InCFShQzsQ39T25gkHhLgSLByyAz+Kjb+c2L98w==}
    engines: {node: '>=12'}
    dependencies:
      clean-stack: 4.2.0
      indent-string: 5.0.0
    dev: true

  /ajv-errors/1.0.1_ajv@6.12.6:
    resolution: {integrity: sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6
    dev: true

  /ajv-formats/1.6.1_ajv@7.2.4:
    resolution: {integrity: sha1-NcfNzSoS1QkXHDe6wy8ujrAQpTY=}
    peerDependencies:
      ajv: ^7.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 7.2.4
    dev: true

  /ajv-formats/2.1.1_ajv@8.17.1:
    resolution: {integrity: sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 8.17.1

  /ajv-keywords/3.5.2_ajv@6.12.6:
    resolution: {integrity: sha1-MfKdpatuANHC0yms97WSlhTVAU0=}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6
    dev: true

  /ajv-keywords/5.1.0_ajv@8.17.1:
    resolution: {integrity: sha1-adTThaRzPNvqtElkoRcKiPh/DhY=}
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  /ajv/6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ajv/7.2.4:
    resolution: {integrity: sha1-jiOdTVbPiEvMyozKNi9QhEbcFg8=}
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1
    dev: true

  /ajv/8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  /ali-oss/6.16.0_co@4.6.0:
    resolution: {integrity: sha1-O3++EPE/vVNUePwxx9Bar0KAJps=}
    engines: {node: '>=8'}
    dependencies:
      address: 1.2.2
      agentkeepalive: 3.5.3
      bowser: 1.9.4
      co-defer: 1.0.0_co@4.6.0
      copy-to: 2.0.1
      dateformat: 2.2.0
      debug: 2.6.9
      destroy: 1.2.0
      end-or-error: 1.0.1
      get-ready: 1.0.0
      humanize-ms: 1.2.1
      is-type-of: 1.4.0
      js-base64: 2.6.4
      jstoxml: 0.2.4
      merge-descriptors: 1.0.3
      mime: 2.6.0
      mz-modules: 2.1.0
      platform: 1.3.6
      pump: 3.0.3
      sdk-base: 2.0.1
      stream-http: 2.8.2
      stream-wormhole: 1.1.0
      urllib: 2.44.0
      utility: 1.18.0
      xml2js: 0.4.23
    transitivePeerDependencies:
      - co
      - proxy-agent
      - supports-color
    dev: true

  /ali-oss/6.23.0:
    resolution: {integrity: sha512-FipRmyd16Pr/tEey/YaaQ/24Pc3HEpLM9S1DRakEuXlSLXNIJnu1oJtHM53eVYpvW3dXapSjrip3xylZUTIZVQ==}
    engines: {node: '>=8'}
    dependencies:
      address: 1.2.2
      agentkeepalive: 3.5.3
      bowser: 1.9.4
      copy-to: 2.0.1
      dateformat: 2.2.0
      debug: 4.4.1
      destroy: 1.2.0
      end-or-error: 1.0.1
      get-ready: 1.0.0
      humanize-ms: 1.2.1
      is-type-of: 1.4.0
      js-base64: 2.6.4
      jstoxml: 2.2.9
      lodash: 4.17.21
      merge-descriptors: 1.0.3
      mime: 2.6.0
      platform: 1.3.6
      pump: 3.0.3
      qs: 6.14.0
      sdk-base: 2.0.1
      stream-http: 2.8.2
      stream-wormhole: 1.1.0
      urllib: 2.44.0
      utility: 1.18.0
      xml2js: 0.6.2
    transitivePeerDependencies:
      - proxy-agent
      - supports-color
    dev: true

  /animated-scroll-to/2.3.2:
    resolution: {integrity: sha512-GBX6+V06anH2rRt1sGmYXvAVpBo0tMmJxVvGuMcd+KV+EzMnYFSYBnccCa5IYWJvEigI+vFLKKLLEeO6SyfjcQ==}
    dev: false

  /ansi-colors/3.2.4:
    resolution: {integrity: sha512-hHUXGagefjN2iRrID63xckIvotOXOojhQKWIPUZ4mNUZ9nLZW+7FMNoE1lOkEhNWYsx/7ysGIuJYCiMAA9FnrA==}
    engines: {node: '>=6'}
    dev: true

  /ansi-colors/4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}
    dev: true

  /ansi-escapes/3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==}
    engines: {node: '>=4'}
    dev: true

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-gray/0.1.1:
    resolution: {integrity: sha1-KWLPVOyXksSFEKPetSRDaGHvclE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-wrap: 0.1.0
    dev: true

  /ansi-html-community/0.0.8:
    resolution: {integrity: sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  /ansi-regex/2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-regex/3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}
    dev: true

  /ansi-regex/5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}
    dev: true

  /ansi-regex/6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}
    dev: true

  /ansi-styles/2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /ansi-styles/6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}
    dev: true

  /ansi-wrap/0.1.0:
    resolution: {integrity: sha1-qCJQ3bABXponyoLoLqYDu/pF768=}
    engines: {node: '>=0.10.0'}
    dev: true

  /any-observable/0.3.0_rxjs@6.6.7:
    resolution: {integrity: sha1-r5M0deWAamfQ198JDdXovvZdEZs=}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zenObservable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zenObservable:
        optional: true
    dependencies:
      rxjs: 6.6.7
    dev: true

  /any-promise/1.3.0:
    resolution: {integrity: sha1-q8av7tzqUugJzcA3au0845Y10X8=}
    dev: true

  /anymatch/3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /aproba/1.2.0:
    resolution: {integrity: sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==}
    dev: true

  /aproba/2.1.0:
    resolution: {integrity: sha512-tLIEcj5GuR2RSTnxNKdkK0dJ/GrC7P38sUkiDmDuHfsHmbagTFAxDVIBltoklXEVIQ/f14IL8IMJ5pn9Hez1Ew==}
    dev: true

  /archiver-utils/4.0.1:
    resolution: {integrity: sha512-Q4Q99idbvzmgCTEAAhi32BkOyq8iVI5EwdO0PmBDSGIzzjYNdcFn7Q7k3OzbLy4kLUPXfJtG6fO2RjftXbobBg==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      glob: 8.0.3
      graceful-fs: 4.2.11
      lazystream: 1.0.1
      lodash: 4.17.21
      normalize-path: 3.0.0
      readable-stream: 3.6.2
    dev: true

  /archiver/6.0.2:
    resolution: {integrity: sha512-UQ/2nW7NMl1G+1UnrLypQw1VdT9XZg/ECcKPq7l+STzStrSivFIXIp34D8M5zeNGW5NoOupdYCHv6VySCPNNlw==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      archiver-utils: 4.0.1
      async: 3.2.6
      buffer-crc32: 0.2.13
      readable-stream: 3.6.2
      readdir-glob: 1.1.3
      tar-stream: 3.1.7
      zip-stream: 5.0.2
    dev: true

  /are-we-there-yet/1.1.7:
    resolution: {integrity: sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=}
    dependencies:
      delegates: 1.0.0
      readable-stream: 2.3.8
    dev: true

  /argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: true

  /argparse/2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=}
    dev: false

  /aria-query/5.3.2:
    resolution: {integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==}
    engines: {node: '>= 0.4'}
    dev: true

  /array-buffer-byte-length/1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5
    dev: true

  /array-differ/3.0.0:
    resolution: {integrity: sha1-PLs9DzFoEOr8xHYkc0I31q7krms=}
    engines: {node: '>=8'}
    dev: true

  /array-each/1.0.1:
    resolution: {integrity: sha1-p5SvDAWrF1KEbudTofIRoFugxE8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-flatten/1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=}

  /array-flatten/2.1.2:
    resolution: {integrity: sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==}
    dev: true

  /array-ify/1.0.0:
    resolution: {integrity: sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=}
    dev: true

  /array-includes/3.1.9:
    resolution: {integrity: sha512-FmeCCAenzH0KH381SPT5FZmiA/TmpndpcaShhfgEN9eCVjnFBqq3l1xrI42y8+PPLI6hypzou4GXw00WHmPBLQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1
      math-intrinsics: 1.1.0
    dev: true

  /array-slice/1.1.0:
    resolution: {integrity: sha1-42jqFfibxwaff/uJrsOmx9SsItQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-union/1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-uniq: 1.0.3
    dev: true

  /array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /array-uniq/1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array.prototype.findlast/1.2.5:
    resolution: {integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.findlastindex/1.2.6:
    resolution: {integrity: sha512-F/TKATkzseUExPlfvmwQKGITM3DGTK+vkAsCZoDc5daVygbJBnjEUCbgkAvVFsgfXfX4YIqZ/27G3k3tdXrTxQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.flat/1.3.3:
    resolution: {integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.flatmap/1.3.3:
    resolution: {integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.reduce/1.0.8:
    resolution: {integrity: sha512-DwuEqgXFBwbmZSRqt3BpQigWNUoqw9Ml2dTWdF3B2zQlQX4OeUE0zyuzX0fX0IbTvjdkZbcBTU3idgpO78qkTw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-array-method-boxes-properly: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      is-string: 1.1.1
    dev: true

  /array.prototype.tosorted/1.1.4:
    resolution: {integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0
    dev: true

  /arraybuffer.prototype.slice/1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5
    dev: true

  /arrify/1.0.1:
    resolution: {integrity: sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /arrify/2.0.1:
    resolution: {integrity: sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==}
    engines: {node: '>=8'}
    dev: true

  /asap/2.0.6:
    resolution: {integrity: sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=}

  /asar/3.2.0:
    resolution: {integrity: sha512-COdw2ZQvKdFGFxXwX3oYh2/sOsJWJegrdJCGxnN4MZ7IULgRBp9P6665aqj9z1v9VwP4oP1hRBojRDQ//IGgAg==}
    engines: {node: '>=10.12.0'}
    hasBin: true
    dependencies:
      chromium-pickle-js: 0.2.0
      commander: 5.1.0
      glob: 7.2.3
      minimatch: 3.1.2
    optionalDependencies:
      '@types/glob': 7.2.0
    dev: true

  /asn1/0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=}
    dependencies:
      safer-buffer: 2.1.2
    dev: true

  /assert-plus/1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=}
    engines: {node: '>=0.8'}
    dev: true

  /ast-types-flow/0.0.8:
    resolution: {integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==}
    dev: true

  /astral-regex/2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}
    dev: true

  /async-function/1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}
    dev: true

  /async/2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}
    dependencies:
      lodash: 4.17.21
    dev: true

  /async/3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==}
    dev: true

  /asynckit/0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}
    dev: true

  /at-least-node/1.0.0:
    resolution: {integrity: sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=}
    engines: {node: '>= 4.0.0'}
    dev: true

  /atomically/1.7.0:
    resolution: {integrity: sha1-wHoEWEMuptvJo1Bv/6QktIvMqv4=}
    engines: {node: '>=10.12.0'}
    dev: true

  /available-typed-arrays/1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      possible-typed-array-names: 1.1.0
    dev: true

  /aws-sign2/0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=}
    dev: true

  /aws4/1.13.2:
    resolution: {integrity: sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==}
    dev: true

  /axe-core/4.10.3:
    resolution: {integrity: sha512-Xm7bpRXnDSX2YE2YFfBk2FnF0ep6tmG7xPh8iHee8MIcrgq762Nkce856dYtJYLkuIoYZvGfTs/PbZhideTcEg==}
    engines: {node: '>=4'}
    dev: true

  /axios/0.26.1:
    resolution: {integrity: sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==}
    dependencies:
      follow-redirects: 1.15.9
    transitivePeerDependencies:
      - debug
    dev: true

  /axobject-query/4.1.0:
    resolution: {integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==}
    engines: {node: '>= 0.4'}
    dev: true

  /b4a/1.6.7:
    resolution: {integrity: sha512-OnAYlL5b7LEkALw87fUVafQw5rVR9RjwGd4KUwNQ6DrrNmaVaUCgLipfVlzrPQ4tWOR9P0IXGNOx50jYCCdSJg==}
    dev: true

  /babel-loader/8.4.1_avnu35bd2ytylp2v4gms37f63m:
    resolution: {integrity: sha512-nXzRChX+Z1GoE6yWavBQg6jDslyFF3SDjl2paADuoQtQW10JqShJt62R6eJQ5m/pjJFDT8xgKIWSP85OY8eXeA==}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.28.0
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /babel-plugin-polyfill-corejs2/0.4.14_@babel+core@7.28.0:
    resolution: {integrity: sha512-Co2Y9wX854ts6U8gAAPXfn0GmAyctHuK8n0Yhfjd6t30g7yvKjspvvOo9yG+z52PZRgFErt7Ka2pYnXCjLKEpg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5_@babel+core@7.28.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-corejs3/0.13.0_@babel+core@7.28.0:
    resolution: {integrity: sha512-U+GNwMdSFgzVmfhNm8GJUX88AadB3uo9KpJqS3FaqNIPKgySuvMb+bHPsOmmuWyIcuqZj/pzt1RUIUZns4y2+A==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5_@babel+core@7.28.0
      core-js-compat: 3.44.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-regenerator/0.6.5_@babel+core@7.28.0:
    resolution: {integrity: sha512-ISqQ2frbiNU9vIJkzg7dlPpznPZ4jOiUQ1uSmB0fEHeowtN3COYRsXr/xexn64NpU13P06jc/L5TgiJXOgrbEg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5_@babel+core@7.28.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-runtime/6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=}

  /bare-events/2.6.0:
    resolution: {integrity: sha512-EKZ5BTXYExaNqi3I3f9RtEsaI/xBSGjE0XZCZilPzFAV/goswFHuPd9jEZlPIZ/iNZJwDSao9qRiScySz7MbQg==}
    requiresBuild: true
    dev: true
    optional: true

  /base64-js/1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=}
    dev: true

  /batch/0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=}

  /bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=}
    dependencies:
      tweetnacl: 0.14.5
    dev: true

  /before-after-hook/2.2.3:
    resolution: {integrity: sha512-NzUnlZexiaH/46WDhANlyR2bXRopNg4F/zuSA3OpZnllCUgRaOF2znDioDWrmbNVsuZk6l9pMquQB38cfBZwkQ==}
    dev: true

  /bezier-easing/2.1.0:
    resolution: {integrity: sha1-wE3+i5JtbsrKGBPWn/F5t8ICXYY=}
    dev: true

  /big.js/3.2.0:
    resolution: {integrity: sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q==}
    dev: true

  /big.js/5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}
    dev: true

  /big.js/6.2.2:
    resolution: {integrity: sha512-y/ie+Faknx7sZA5MfGA2xKlu0GDv8RWrXGsmlteyJQ2lvoKv9GBK/fpRMc2qlSoBAgNxrixICFCBefIq8WCQpQ==}
    dev: false

  /binary-extensions/2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  /bl/4.1.0:
    resolution: {integrity: sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=}
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2
    dev: true

  /bluebird/3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}
    dev: true

  /body-parser/1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  /bonjour-service/1.3.0:
    resolution: {integrity: sha512-3YuAUiSkWykd+2Azjgyxei8OWf8thdn8AITIog2M4UICzoqfjlqr64WIjEXZllf/W6vK1goqleSR6brGomxQqA==}
    dependencies:
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  /bonjour/3.5.0:
    resolution: {integrity: sha512-RaVTblr+OnEli0r/ud8InrU7D+G0y6aJhlxaLa6Pwty4+xoxboF1BsUI45tujvRpbj9dQVoglChqonGAsjEBYg==}
    dependencies:
      array-flatten: 2.1.2
      deep-equal: 1.1.2
      dns-equal: 1.0.0
      dns-txt: 2.0.2
      multicast-dns: 6.2.3
      multicast-dns-service-types: 1.1.0
    dev: true

  /boolbase/1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=}
    dev: true

  /bowser/1.9.4:
    resolution: {integrity: sha512-9IdMmj2KjigRq6oWhmwv1W36pDuA4STQZ8q6YO9um+x07xgYNCD3Oou+WP/3L1HNz7iqythGet3/p4wvc8AAwQ==}
    dev: true

  /brace-expansion/1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion/2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1

  /browserslist/4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001727
      electron-to-chromium: 1.5.183
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3_browserslist@4.25.1

  /buffer-crc32/0.2.13:
    resolution: {integrity: sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=}
    dev: true

  /buffer-from/1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=}

  /buffer-indexof/1.1.1:
    resolution: {integrity: sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==}
    dev: true

  /buffer/5.7.1:
    resolution: {integrity: sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: true

  /builtin-status-codes/3.0.0:
    resolution: {integrity: sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=}
    dev: true

  /builtins/1.0.3:
    resolution: {integrity: sha1-y5T662HIaWRR2zZTThQi+U8K7og=}
    dev: true

  /byline/5.0.0:
    resolution: {integrity: sha1-dBxSFkaOrcRXsDQQEYrXfejB3bE=}
    engines: {node: '>=0.10.0'}
    dev: true

  /byte-size/7.0.1:
    resolution: {integrity: sha1-sdrzOG3nq51wa5QadI2/xxEw3uM=}
    engines: {node: '>=10'}
    dev: true

  /bytes/3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  /cacache/12.0.4:
    resolution: {integrity: sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1_bluebird@3.7.2
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3
    dev: true

  /cacache/15.3.0:
    resolution: {integrity: sha1-3IU4D7L1Vv492kxxm/oOyHWn8es=}
    engines: {node: '>= 10'}
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    dev: true

  /cache-content-type/1.0.1:
    resolution: {integrity: sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==}
    engines: {node: '>= 6.0.0'}
    dependencies:
      mime-types: 2.1.35
      ylru: 1.4.0
    dev: true

  /cacheable-lookup/5.0.4:
    resolution: {integrity: sha1-WmuGWyxENXvj1evCpGewMnGacAU=}
    engines: {node: '>=10.6.0'}
    dev: true

  /cacheable-request/7.0.4:
    resolution: {integrity: sha512-v+p6ongsrp0yTGbJXjgxPow2+DL93DASP4kXCDKb8/bwRtt9OEF3whggkkDkGNzgcWy2XaF4a8nZglC7uElscg==}
    engines: {node: '>=8'}
    dependencies:
      clone-response: 1.0.3
      get-stream: 5.2.0
      http-cache-semantics: 4.2.0
      keyv: 4.5.4
      lowercase-keys: 2.0.0
      normalize-url: 6.1.0
      responselike: 2.0.1
    dev: true

  /call-bind-apply-helpers/1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  /call-bind/1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2
    dev: true

  /call-bound/1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /camel-case/4.1.2:
    resolution: {integrity: sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.8.1
    dev: true

  /camelcase-keys/6.2.2:
    resolution: {integrity: sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1
    dev: true

  /camelcase/5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: true

  /caniuse-lite/1.0.30001727:
    resolution: {integrity: sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==}

  /capital-case/1.0.4:
    resolution: {integrity: sha1-nRMCkjU8kkn2sA+lhSvuOKcX5mk=}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
      upper-case-first: 2.0.2
    dev: true

  /case-sensitive-paths-webpack-plugin/2.4.0:
    resolution: {integrity: sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=}
    engines: {node: '>=4'}
    dev: true

  /caseless/0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=}
    dev: true

  /chalk/1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: true

  /chalk/2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chalk/5.4.1:
    resolution: {integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}
    dev: true

  /change-case/4.1.2:
    resolution: {integrity: sha1-/t/F8TYEXiOYwEEO5EH5VwRkHhI=}
    dependencies:
      camel-case: 4.1.2
      capital-case: 1.0.4
      constant-case: 3.0.4
      dot-case: 3.0.4
      header-case: 2.0.4
      no-case: 3.0.4
      param-case: 3.0.4
      pascal-case: 3.1.2
      path-case: 3.0.4
      sentence-case: 3.0.4
      snake-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /chardet/0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}
    dev: true

  /chokidar/3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /chokidar/3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  /chownr/1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}
    dev: true

  /chownr/2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}
    dev: true

  /chrome-trace-event/1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}

  /chromium-pickle-js/0.2.0:
    resolution: {integrity: sha1-BKEGZywYsIWrd02YPfo+oTjyIgU=}
    dev: true

  /ci-info/2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}
    dev: true

  /classnames/2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}
    dev: false

  /clean-css/5.3.3:
    resolution: {integrity: sha512-D5J+kHaVb/wKSFcyyV75uCn8fiY4sV38XJoe4CUyGQ+mOU/fMVYUdH1hJC+CJQ5uY3EnW27SbJYS4X8BiLrAFg==}
    engines: {node: '>= 10.0'}
    dependencies:
      source-map: 0.6.1
    dev: true

  /clean-stack/2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: true

  /clean-stack/4.2.0:
    resolution: {integrity: sha512-LYv6XPxoyODi36Dp976riBtSY27VmFo+MKqEU9QCCWyTrdEPDog+RWA7xQWHi6Vbp61j5c4cdzzX1NidnwtUWg==}
    engines: {node: '>=12'}
    dependencies:
      escape-string-regexp: 5.0.0
    dev: true

  /clean-webpack-plugin/4.0.0_webpack@5.100.1:
    resolution: {integrity: sha1-cpR9RAPUUvOO1hqf8K2oEiqs1yk=}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      webpack: '>=4.0.0 <6.0.0'
    dependencies:
      del: 4.1.1
      webpack: 5.100.1
    dev: true

  /cli-cursor/2.1.0:
    resolution: {integrity: sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=}
    engines: {node: '>=4'}
    dependencies:
      restore-cursor: 2.0.0
    dev: true

  /cli-cursor/3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /cli-cursor/5.0.0:
    resolution: {integrity: sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==}
    engines: {node: '>=18'}
    dependencies:
      restore-cursor: 5.1.0
    dev: true

  /cli-spinners/2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}
    dev: true

  /cli-truncate/0.2.1:
    resolution: {integrity: sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      slice-ansi: 0.0.4
      string-width: 1.0.2
    dev: true

  /cli-truncate/2.1.0:
    resolution: {integrity: sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=}
    engines: {node: '>=8'}
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3
    dev: true

  /cli-width/3.0.0:
    resolution: {integrity: sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=}
    engines: {node: '>= 10'}
    dev: true

  /cli-width/4.1.0:
    resolution: {integrity: sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==}
    engines: {node: '>= 12'}
    dev: true

  /cliui/6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0
    dev: true

  /cliui/7.0.4:
    resolution: {integrity: sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /cliui/8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /clone-deep/4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  /clone-response/1.0.3:
    resolution: {integrity: sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==}
    dependencies:
      mimic-response: 1.0.1
    dev: true

  /clone/1.0.4:
    resolution: {integrity: sha1-2jCcwmPfFZlMaIypAheco8fNfH4=}
    engines: {node: '>=0.8'}
    dev: true

  /cluster-key-slot/1.1.0:
    resolution: {integrity: sha512-2Nii8p3RwAPiFwsnZvukotvow2rIHM+yQ6ZcBXGHdniadkYGZYiGmkHJIbZPIV9nfv7m/U1IPMVVcAhoWFeklw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /cmd-shim/4.1.0:
    resolution: {integrity: sha1-s6kEpnQ+n+3kFIxvOAC/KggTW90=}
    engines: {node: '>=10'}
    dependencies:
      mkdirp-infer-owner: 2.0.0
    dev: true

  /co-body/6.2.0:
    resolution: {integrity: sha512-Kbpv2Yd1NdL1V/V4cwLVxraHDV6K8ayohr2rmH0J87Er8+zJjcTa6dAn9QMPC9CRgU8+aNajKbSf1TzDB1yKPA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@hapi/bourne': 3.0.0
      inflation: 2.1.0
      qs: 6.14.0
      raw-body: 2.5.2
      type-is: 1.6.18
    dev: true

  /co-defer/1.0.0_co@4.6.0:
    resolution: {integrity: sha1-Pkp4eo7tawoh7ih8CU9+jeDTyBg=}
    engines: {node: '>= 0.11.14'}
    peerDependencies:
      co: '4'
    dependencies:
      co: 4.6.0
    dev: true

  /co/4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}
    dev: true

  /code-point-at/1.1.0:
    resolution: {integrity: sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=}
    engines: {node: '>=0.10.0'}
    dev: true

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name/1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}
    dev: true

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: true

  /color-normalize/1.5.2:
    resolution: {integrity: sha512-yYMIoyFJmUoKbCK6sBShljBWfkt8DXVfaZJn9/zvRJkF9eQJDbZhcYC6LdOVy40p4tfVwYYb9cXl8oqpu7pzBw==}
    dependencies:
      color-rgba: 2.4.0
      dtype: 2.0.0
    dev: true

  /color-parse/1.4.3:
    resolution: {integrity: sha512-BADfVl/FHkQkyo8sRBwMYBqemqsgnu7JZAwUgvBvuwwuNUZAhSvLTbsEErS5bQXzOjDR0dWzJ4vXN2Q+QoPx0A==}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-rgba/2.4.0:
    resolution: {integrity: sha512-Nti4qbzr/z2LbUWySr7H9dk3Rl7gZt7ihHAxlgT4Ho90EXWkjtkL1avTleu9yeGuqrt/chxTB6GKK8nZZ6V0+Q==}
    dependencies:
      color-parse: 1.4.3
      color-space: 2.3.2
    dev: true

  /color-space/2.3.2:
    resolution: {integrity: sha512-BcKnbOEsOarCwyoLstcoEztwT0IJxqqQkNwDuA3a65sICvvHL2yoeV13psoDFh5IuiOMnIOKdQDwB4Mk3BypiA==}
    dev: true

  /color-string/1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: true

  /color-support/1.1.3:
    resolution: {integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==}
    hasBin: true
    dev: true

  /color/3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1
    dev: true

  /colorette/2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}

  /colors/1.4.0:
    resolution: {integrity: sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA==}
    engines: {node: '>=0.1.90'}
    dev: true

  /colorspace/1.1.4:
    resolution: {integrity: sha512-BgvKJiuVu1igBUF2kEjRCZXol6wiiGbY5ipL/oVPwm0BL9sIpMIzM8IK7vwuxIIzOXMV3Ey5w+vxhm0rR/TN8w==}
    dependencies:
      color: 3.2.1
      text-hex: 1.0.0
    dev: true

  /columnify/1.6.0:
    resolution: {integrity: sha512-lomjuFZKfM6MSAnV9aCZC9sc0qGbmZdfygNv+nCpqVkSKdCxCklLtd16O0EILGkImHw9ZpHkAnHaB+8Zxq5W6Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      strip-ansi: 6.0.1
      wcwidth: 1.0.1
    dev: true

  /combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: true

  /commander/10.0.1:
    resolution: {integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==}
    engines: {node: '>=14'}

  /commander/2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  /commander/5.1.0:
    resolution: {integrity: sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=}
    engines: {node: '>= 6'}
    dev: true

  /commander/6.2.1:
    resolution: {integrity: sha1-B5LraC37wyWZm7K4T93duhEKxzw=}
    engines: {node: '>= 6'}
    dev: true

  /commander/8.3.0:
    resolution: {integrity: sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=}
    engines: {node: '>= 12'}
    dev: true

  /common-path-prefix/3.0.0:
    resolution: {integrity: sha1-fQB6fgfFjEtNX0MxMaGRQbKfEeA=}
    dev: true

  /commondir/1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=}
    dev: true

  /compare-func/2.0.0:
    resolution: {integrity: sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=}
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0
    dev: true

  /compress-commons/5.0.3:
    resolution: {integrity: sha512-/UIcLWvwAQyVibgpQDPtfNM3SvqN7G9elAPAV7GM0L53EbNWwWiCsWtK8Fwed/APEbptPHXs5PuW+y8Bq8lFTA==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      crc-32: 1.2.2
      crc32-stream: 5.0.1
      normalize-path: 3.0.0
      readable-stream: 3.6.2
    dev: true

  /compressible/2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.54.0

  /compression/1.8.0:
    resolution: {integrity: sha512-k6WLKfunuqCYD3t6AsuPGvQWaKwuLLh2/xHNcX4qE+vIfDNXpSqnrhwA7O53R7WVQUnt8dVAIW+YHr7xTgOgGA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  /concat-map/0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  /concat-stream/1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6
    dev: true

  /concat-stream/2.0.0:
    resolution: {integrity: sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==}
    engines: {'0': node >= 6.0}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
      typedarray: 0.0.6
    dev: true

  /conf/9.0.2:
    resolution: {integrity: sha1-lDWJYCsc4nTZI0JlMUM2ppiXK8U=}
    engines: {node: '>=10'}
    dependencies:
      ajv: 7.2.4
      ajv-formats: 1.6.1_ajv@7.2.4
      atomically: 1.7.0
      debounce-fn: 4.0.0
      dot-prop: 6.0.1
      env-paths: 2.2.1
      json-schema-typed: 7.0.3
      make-dir: 3.1.0
      onetime: 5.1.2
      pkg-up: 3.1.0
      semver: 7.7.2
    dev: true

  /config-chain/1.1.13:
    resolution: {integrity: sha1-+tB5Wqamza/57Rto6d/5Q3LCMvQ=}
    dependencies:
      ini: 1.3.8
      proto-list: 1.2.4
    dev: true

  /connect-history-api-fallback/1.6.0:
    resolution: {integrity: sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==}
    engines: {node: '>=0.8'}
    dev: true

  /connect-history-api-fallback/2.0.0:
    resolution: {integrity: sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==}
    engines: {node: '>=0.8'}

  /consola/3.4.2:
    resolution: {integrity: sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dev: true

  /console-control-strings/1.1.0:
    resolution: {integrity: sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=}
    dev: true

  /constant-case/3.0.4:
    resolution: {integrity: sha1-O4Sprq9M8x7EXmv13pG9+wWJ+vE=}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
      upper-case: 2.0.2
    dev: true

  /content-disposition/0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1

  /content-type/1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  /conventional-changelog-angular/5.0.13:
    resolution: {integrity: sha1-iWiF1juRSnDUk0tZ0v573hgysow=}
    engines: {node: '>=10'}
    dependencies:
      compare-func: 2.0.0
      q: 1.5.1
    dev: true

  /conventional-changelog-core/4.2.4:
    resolution: {integrity: sha1-5Q0Efo66z2P6w9xnv5GBdwAeHp8=}
    engines: {node: '>=10'}
    dependencies:
      add-stream: 1.0.0
      conventional-changelog-writer: 5.0.1
      conventional-commits-parser: 3.2.4
      dateformat: 3.0.3
      get-pkg-repo: 4.2.1
      git-raw-commits: 2.0.11
      git-remote-origin-url: 2.0.0
      git-semver-tags: 4.1.1
      lodash: 4.17.21
      normalize-package-data: 3.0.3
      q: 1.5.1
      read-pkg: 3.0.0
      read-pkg-up: 3.0.0
      through2: 4.0.2
    dev: true

  /conventional-changelog-preset-loader/2.3.4:
    resolution: {integrity: sha1-FKhVq7/9WQJ/1gJYHx802YYupEw=}
    engines: {node: '>=10'}
    dev: true

  /conventional-changelog-writer/5.0.1:
    resolution: {integrity: sha512-5WsuKUfxW7suLblAbFnxAcrvf6r+0b7GvNaWUwUIk0bXMnENP/PEieGKVUQrjPqwPT4o3EPAASBXiY6iHooLOQ==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      conventional-commits-filter: 2.0.7
      dateformat: 3.0.3
      handlebars: 4.7.8
      json-stringify-safe: 5.0.1
      lodash: 4.17.21
      meow: 8.1.2
      semver: 6.3.1
      split: 1.0.1
      through2: 4.0.2
    dev: true

  /conventional-commits-filter/2.0.7:
    resolution: {integrity: sha1-+Nm08YL84Aya9xOdpJNlsTbIoLM=}
    engines: {node: '>=10'}
    dependencies:
      lodash.ismatch: 4.4.0
      modify-values: 1.0.1
    dev: true

  /conventional-commits-parser/3.2.4:
    resolution: {integrity: sha512-nK7sAtfi+QXbxHCYfhpZsfRtaitZLIA6889kFIouLvz6repszQDgxBu7wf2WbU+Dco7sAnNCJYERCwt54WPC2Q==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 1.0.1
      lodash: 4.17.21
      meow: 8.1.2
      split2: 3.2.2
      through2: 4.0.2
    dev: true

  /conventional-recommended-bump/6.1.0:
    resolution: {integrity: sha1-z6YjKF0d5VQBLy/95w2ciiIjH1U=}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      concat-stream: 2.0.0
      conventional-changelog-preset-loader: 2.3.4
      conventional-commits-filter: 2.0.7
      conventional-commits-parser: 3.2.4
      git-raw-commits: 2.0.11
      git-semver-tags: 4.1.1
      meow: 8.1.2
      q: 1.5.1
    dev: true

  /convert-source-map/2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}
    dev: true

  /cookie-signature/1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=}

  /cookie/0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}

  /cookies/0.9.1:
    resolution: {integrity: sha512-TG2hpqe4ELx54QER/S3HQ9SRVnQnGBtKUz5bLQWtYAQ+o6GpgMs6sYUvaiJjVxb+UXwhRhAEP3m7LbsIZ77Hmw==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      keygrip: 1.1.0
    dev: true

  /copy-concurrently/1.0.5:
    resolution: {integrity: sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==}
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: true

  /copy-to/2.0.1:
    resolution: {integrity: sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=}
    dev: true

  /copy-webpack-plugin/5.1.2_webpack@5.100.1:
    resolution: {integrity: sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI=}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      glob-parent: 3.1.0
      globby: 7.1.1
      is-glob: 4.0.3
      loader-utils: 1.4.2
      minimatch: 3.1.2
      normalize-path: 3.0.0
      p-limit: 2.3.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-log: 2.0.0
    dev: true

  /core-js-compat/3.44.0:
    resolution: {integrity: sha512-JepmAj2zfl6ogy34qfWtcE7nHKAJnKsQFRn++scjVS2bZFllwptzw61BZcZFYBPpUznLfAvh0LGhxKppk04ClA==}
    dependencies:
      browserslist: 4.25.1
    dev: true

  /core-js-pure/3.44.0:
    resolution: {integrity: sha512-gvMQAGB4dfVUxpYD0k3Fq8J+n5bB6Ytl15lqlZrOIXFzxOhtPaObfkQGHtMRdyjIf7z2IeNULwi1jEwyS+ltKQ==}
    requiresBuild: true
    dev: true

  /core-js/2.6.12:
    resolution: {integrity: sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=}
    deprecated: core-js@<3 is no longer maintained and not recommended for usage due to the number of issues. Please, upgrade your dependencies to the actual version of core-js@3.
    requiresBuild: true

  /core-util-is/1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=}
    dev: true

  /core-util-is/1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=}

  /cos-nodejs-sdk-v5/2.11.4:
    resolution: {integrity: sha512-V26WkAYYT+r+e1d+DywpM23L8cYlYhgXjMepnGVE7BNQVUB3rWl8X0bpUmW2P76h9BDYyioJqBUSjZw6tFJOXQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@types/node': 14.18.63
      conf: 9.0.2
      mime-types: 2.1.35
      request: 2.88.2
      xml2js: 0.4.23
    dev: true

  /cos-nodejs-sdk-v5/2.15.1:
    resolution: {integrity: sha512-eBfqnuffe1qlIHbCep8eQgXa6Fq44cDjgCv6A2pyB/DAnKxY15DE6vU92Zc55v2t60EGcDapaTQY6DWVauGMRQ==}
    engines: {node: '>= 6'}
    dependencies:
      conf: 9.0.2
      fast-xml-parser: 4.2.5
      mime-types: 2.1.35
      request: 2.88.2
    dev: true

  /cosmiconfig/7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /crc-32/1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dev: true

  /crc32-stream/5.0.1:
    resolution: {integrity: sha512-lO1dFui+CEUh/ztYIpgpKItKW9Bb4NWakCRJrnqAbFIYD+OZAwb2VfD5T5eXMw2FNcsDHkQcNl/Wh3iVXYwU6g==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      crc-32: 1.2.2
      readable-stream: 3.6.2
    dev: true

  /cross-env-os/7.1.1:
    resolution: {integrity: sha512-KQOyhqKrxHnx7wflQTYnH6fQUd0tblPA+OI62FJPdH/cOvra70CIj86aBZ9TLVdmmQkrfUzkihOQs1LMXlyRPw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true
    dependencies:
      cross-spawn: 7.0.6
      node-version-compare: 1.0.3
    dev: true

  /cross-env/7.0.3:
    resolution: {integrity: sha1-hlJkspZ33AFbqEGJGJZd0jL8VM8=}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true
    dependencies:
      cross-spawn: 7.0.6
    dev: true

  /cross-fetch/3.2.0:
    resolution: {integrity: sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==}
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding
    dev: false

  /cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  /crypto-js/4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}
    dev: false

  /css-loader/3.6.0_webpack@5.100.1:
    resolution: {integrity: sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      camelcase: 5.3.1
      cssesc: 3.0.0
      icss-utils: 4.1.1
      loader-utils: 1.4.2
      normalize-path: 3.0.0
      postcss: 7.0.39
      postcss-modules-extract-imports: 2.0.0
      postcss-modules-local-by-default: 3.0.3
      postcss-modules-scope: 2.2.0
      postcss-modules-values: 3.0.0
      postcss-value-parser: 4.2.0
      schema-utils: 2.7.1
      semver: 6.3.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /css-select/4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.2.2
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: true

  /css-what/6.2.2:
    resolution: {integrity: sha512-u/O3vwbptzhMs3L1fQE82ZSLHQQfto5gyZzwteVIEyeaY5Fc7R4dapF/BvRoSYFeqfBk4m0V1Vafq5Pjv25wvA==}
    engines: {node: '>= 6'}
    dev: true

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}
    dev: true

  /cyclist/1.0.2:
    resolution: {integrity: sha512-0sVXIohTfLqVIW3kb/0n6IiWF3Ifj5nm2XaSrLq2DI6fKIGa2fYAZdk917rUneaeLVpYfFcyXE2ft0fe3remsA==}
    dev: true

  /damerau-levenshtein/1.0.8:
    resolution: {integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==}
    dev: true

  /dargs/7.0.0:
    resolution: {integrity: sha1-BAFcQd4Ly2nshAUPPZvgyvjW1cw=}
    engines: {node: '>=8'}
    dev: true

  /dashdash/1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0
    dev: true

  /data-view-buffer/1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /data-view-byte-length/1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /data-view-byte-offset/1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /date-fns/1.30.1:
    resolution: {integrity: sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=}
    dev: true

  /dateformat/2.2.0:
    resolution: {integrity: sha1-QGXiATz5+5Ft39gu+1Bq1MZ2kGI=}
    dev: true

  /dateformat/3.0.3:
    resolution: {integrity: sha512-jyCETtSl3VMZMWeRo7iY1FL19ges1t55hMo5yaam4Jrsm5EPL89UQkoQRyiI+Yf4k8r2ZpdngkV8hr1lIdjb3Q==}
    dev: true

  /debounce-fn/4.0.0:
    resolution: {integrity: sha1-7XbSBtilDmDeDdZtSU2Cg1/+Ycc=}
    engines: {node: '>=10'}
    dependencies:
      mimic-fn: 3.1.0
    dev: true

  /debug/2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0

  /debug/3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: true

  /debug/4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3

  /debuglog/1.0.1:
    resolution: {integrity: sha1-qiT/uaw9+aI1GDfPstJ5NgzXhJI=}
    dev: true

  /decamelize-keys/1.1.1:
    resolution: {integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1
    dev: true

  /decamelize/1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /decode-uri-component/0.2.2:
    resolution: {integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==}
    engines: {node: '>=0.10'}
    dev: true

  /decompress-response/6.0.0:
    resolution: {integrity: sha1-yjh2Et234QS9FthaqwDV7PCcZvw=}
    engines: {node: '>=10'}
    dependencies:
      mimic-response: 3.1.0
    dev: true

  /dedent/0.7.0:
    resolution: {integrity: sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=}
    dev: true

  /deep-equal/1.0.1:
    resolution: {integrity: sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==}
    dev: true

  /deep-equal/1.1.2:
    resolution: {integrity: sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-arguments: 1.2.0
      is-date-object: 1.1.0
      is-regex: 1.2.1
      object-is: 1.1.6
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.4
    dev: true

  /deep-is/0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}
    dev: true

  /default-gateway/6.0.3:
    resolution: {integrity: sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=}
    engines: {node: '>= 10'}
    dependencies:
      execa: 5.1.1

  /default-user-agent/1.0.0:
    resolution: {integrity: sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY=}
    engines: {node: '>= 0.10.0'}
    dependencies:
      os-name: 1.0.3
    dev: true

  /defaults/1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}
    dependencies:
      clone: 1.0.4
    dev: true

  /defer-to-connect/2.0.1:
    resolution: {integrity: sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc=}
    engines: {node: '>=10'}
    dev: true

  /define-data-property/1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0
    dev: true

  /define-lazy-prop/2.0.0:
    resolution: {integrity: sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=}
    engines: {node: '>=8'}

  /define-properties/1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1
    dev: true

  /del/4.1.1:
    resolution: {integrity: sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ==}
    engines: {node: '>=6'}
    dependencies:
      '@types/glob': 7.2.0
      globby: 6.1.0
      is-path-cwd: 2.2.0
      is-path-in-cwd: 2.1.0
      p-map: 2.1.0
      pify: 4.0.1
      rimraf: 2.7.1
    dev: true

  /del/6.1.1:
    resolution: {integrity: sha512-ua8BhapfP0JUJKC/zV9yHHDW/rDoDxP4Zhn3AkA6/xT6gY7jYXJiaeyBZznYVujhZZET+UgcbZiQ7sN3WqcImg==}
    engines: {node: '>=10'}
    dependencies:
      globby: 11.1.0
      graceful-fs: 4.2.11
      is-glob: 4.0.3
      is-path-cwd: 2.2.0
      is-path-inside: 3.0.3
      p-map: 4.0.0
      rimraf: 3.0.2
      slash: 3.0.0
    dev: true

  /del/7.1.0:
    resolution: {integrity: sha512-v2KyNk7efxhlyHpjEvfyxaAihKKK0nWCuf6ZtqZcFFpQRG0bJ12Qsr0RpvsICMjAAZ8DOVCxrlqpxISlMHC4Kg==}
    engines: {node: '>=14.16'}
    dependencies:
      globby: 13.2.2
      graceful-fs: 4.2.11
      is-glob: 4.0.3
      is-path-cwd: 3.0.0
      is-path-inside: 4.0.0
      p-map: 5.5.0
      rimraf: 3.0.2
      slash: 4.0.0
    dev: true

  /delayed-stream/1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}
    dev: true

  /delegates/1.0.0:
    resolution: {integrity: sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=}
    dev: true

  /depd/1.1.2:
    resolution: {integrity: sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=}
    engines: {node: '>= 0.6'}

  /depd/2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  /deprecation/2.3.1:
    resolution: {integrity: sha1-Y2jL20Cr8zc7UlrIfkomDDpwCRk=}
    dev: true

  /destroy/1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  /detect-file/1.0.0:
    resolution: {integrity: sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=}
    engines: {node: '>=0.10.0'}
    dev: true

  /detect-indent/5.0.0:
    resolution: {integrity: sha1-OHHMCmoALow+Wzz38zYmRnXwa50=}
    engines: {node: '>=4'}
    dev: true

  /detect-indent/6.1.0:
    resolution: {integrity: sha1-WSSF67v2s7GrK+F1yDk9BMoNV+Y=}
    engines: {node: '>=8'}
    dev: true

  /detect-node/2.1.0:
    resolution: {integrity: sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=}

  /detect-port/1.6.1:
    resolution: {integrity: sha512-CmnVc+Hek2egPx1PeTFVta2W78xy2K/9Rkf6cC4T59S50tVnzKj+tnx5mmx5lwvCkujZ4uRrpRSuV+IVs3f90Q==}
    engines: {node: '>= 4.0.0'}
    hasBin: true
    dependencies:
      address: 1.2.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /dezalgo/1.0.4:
    resolution: {integrity: sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==}
    dependencies:
      asap: 2.0.6
      wrappy: 1.0.2
    dev: true

  /digest-header/1.1.0:
    resolution: {integrity: sha512-glXVh42vz40yZb9Cq2oMOt70FIoWiv+vxNvdKdU8CwjLad25qHM3trLxhl9bVjdr6WaslIXhWpn0NO8T/67Qjg==}
    engines: {node: '>= 8.0.0'}
    dev: true

  /dijkstrajs/1.0.3:
    resolution: {integrity: sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==}
    dev: true

  /dir-glob/2.2.2:
    resolution: {integrity: sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw==}
    engines: {node: '>=4'}
    dependencies:
      path-type: 3.0.0
    dev: true

  /dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /dns-equal/1.0.0:
    resolution: {integrity: sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==}
    dev: true

  /dns-packet/1.3.4:
    resolution: {integrity: sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA==}
    dependencies:
      ip: 1.1.9
      safe-buffer: 5.2.1
    dev: true

  /dns-packet/5.6.1:
    resolution: {integrity: sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==}
    engines: {node: '>=6'}
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.5

  /dns-txt/2.0.2:
    resolution: {integrity: sha512-Ix5PrWjphuSoUXV/Zv5gaFHjnaJtb02F2+Si3Ht9dyJ87+Z/lMmy+dpNHtTGraNK958ndXq2i+GLkWsWHcKaBQ==}
    dependencies:
      buffer-indexof: 1.1.1
    dev: true

  /doctrine/2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine/3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-converter/0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==}
    dependencies:
      utila: 0.4.0
    dev: true

  /dom-serializer/1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: true

  /domelementtype/2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: true

  /domhandler/4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: true

  /domutils/2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: true

  /dot-case/3.0.4:
    resolution: {integrity: sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /dot-prop/5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=}
    engines: {node: '>=8'}
    dependencies:
      is-obj: 2.0.0
    dev: true

  /dot-prop/6.0.1:
    resolution: {integrity: sha1-/CazzxQrnlm3Tb057WbOYgxoEIM=}
    engines: {node: '>=10'}
    dependencies:
      is-obj: 2.0.0
    dev: true

  /dotenv/10.0.0:
    resolution: {integrity: sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==}
    engines: {node: '>=10'}
    dev: true

  /dtype/2.0.0:
    resolution: {integrity: sha512-s2YVcLKdFGS0hpFqJaTwscsyt0E8nNFdmo73Ocd81xNPj4URI4rj6D60A+vFMIw7BXWlb4yRkEwfBqcZzPGiZg==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /dunder-proto/1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  /duplexer/0.1.2:
    resolution: {integrity: sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=}
    dev: true

  /duplexify/3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}
    dependencies:
      end-of-stream: 1.4.5
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3
    dev: true

  /eastasianwidth/0.2.0:
    resolution: {integrity: sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=}
    dev: true

  /ecc-jsbn/0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2
    dev: true

  /ee-first/1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=}

  /electron-to-chromium/1.5.183:
    resolution: {integrity: sha512-vCrDBYjQCAEefWGjlK3EpoSKfKbT10pR4XXPdn65q7snuNOZnthoVpBfZPykmDapOKfoD+MMIPG8ZjKyyc9oHA==}

  /elegant-spinner/1.0.1:
    resolution: {integrity: sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /emoji-regex/10.4.0:
    resolution: {integrity: sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==}
    dev: true

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /emoji-regex/9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=}
    dev: true

  /emojis-list/2.1.0:
    resolution: {integrity: sha1-TapNnbAPmBmIDHn6RXrlsJof04k=}
    engines: {node: '>= 0.10'}
    dev: true

  /emojis-list/3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}
    dev: true

  /enabled/2.0.0:
    resolution: {integrity: sha512-AKrN98kuwOzMIdAizXGI86UFBoo26CL21UM763y1h/GMSJ4/OHU9k2YlsmBpyScFo/wbLzWQJBMCW4+IO3/+OQ==}
    dev: true

  /encodeurl/1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=}
    engines: {node: '>= 0.8'}

  /encodeurl/2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  /encoding/0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}
    requiresBuild: true
    dependencies:
      iconv-lite: 0.6.3
    dev: true
    optional: true

  /end-of-stream/1.4.5:
    resolution: {integrity: sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==}
    dependencies:
      once: 1.4.0
    dev: true

  /end-or-error/1.0.1:
    resolution: {integrity: sha1-3HpiEP5403L+4kqLSJnb0VVBTcs=}
    engines: {node: '>= 0.11.14'}
    dev: true

  /enhanced-resolve/4.5.0:
    resolution: {integrity: sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.5.0
      tapable: 1.1.3
    dev: true

  /enhanced-resolve/5.18.2:
    resolution: {integrity: sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==}
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  /enquirer/2.4.1:
    resolution: {integrity: sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==}
    engines: {node: '>=8.6'}
    dependencies:
      ansi-colors: 4.1.3
      strip-ansi: 6.0.1
    dev: true

  /entities/2.1.0:
    resolution: {integrity: sha1-mS0xKc999ocLlsV4WMJJoSD4uLU=}
    dev: false

  /entities/2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=}
    dev: true

  /env-paths/2.2.1:
    resolution: {integrity: sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=}
    engines: {node: '>=6'}
    dev: true

  /envinfo/7.14.0:
    resolution: {integrity: sha512-CO40UI41xDQzhLB1hWyqUKgFhs250pNcGbyGKe1l/e4FSaI/+YE4IMG76GDt0In67WLPACIITC+sOi08x4wIvg==}
    engines: {node: '>=4'}
    hasBin: true

  /err-code/2.0.3:
    resolution: {integrity: sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=}
    dev: true

  /errno/0.1.8:
    resolution: {integrity: sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: true

  /error-ex/1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: true

  /error-stack-parser/2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}
    dependencies:
      stackframe: 1.3.4
    dev: true

  /es-abstract/1.24.0:
    resolution: {integrity: sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19
    dev: true

  /es-array-method-boxes-properly/1.0.0:
    resolution: {integrity: sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=}
    dev: true

  /es-define-property/1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  /es-errors/1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  /es-iterator-helpers/1.2.1:
    resolution: {integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3
    dev: true

  /es-module-lexer/1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}

  /es-object-atoms/1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0

  /es-set-tostringtag/2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: true

  /es-shim-unscopables/1.1.0:
    resolution: {integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /es-to-primitive/1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1
    dev: true

  /escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  /escape-html/1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=}

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}
    dev: true

  /escape-string-regexp/4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=}
    engines: {node: '>=10'}
    dev: true

  /escape-string-regexp/5.0.0:
    resolution: {integrity: sha1-RoMSa1ALYXYvLb66zhgG6L4xscg=}
    engines: {node: '>=12'}
    dev: true

  /eslint-config-prettier/6.15.0_eslint@7.32.0:
    resolution: {integrity: sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=}
    hasBin: true
    peerDependencies:
      eslint: '>=3.14.1'
    dependencies:
      eslint: 7.32.0
      get-stdin: 6.0.0
    dev: true

  /eslint-import-resolver-node/0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-module-utils/2.12.1_lo3sk5lgzxlcb5aoezcnaiw6za:
    resolution: {integrity: sha512-L8jSWTze7K2mTg0vos/RuLRS5soomksDPoJLXIslC7c8Wmut3bx7CPpJijDcBZtxQ5lrbUdM+s0OlNbz0DCDNw==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true
    dependencies:
      '@typescript-eslint/parser': 3.10.1_2de3j2mqba4wgeuiaqz2k7syrm
      debug: 3.2.7
      eslint: 7.32.0
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-plugin-import/2.32.0_6vpcbutzmq5vtliwzwjgeopqvm:
    resolution: {integrity: sha512-whOE1HFo/qJDyX4SnXzP4N6zOWn79WhnCUY/iDR0mPfQZO8wcYE4JClzI2oZrhBnnMUCBCHZhO6VQyoBU95mZA==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      '@rtsao/scc': 1.1.0
      '@typescript-eslint/parser': 3.10.1_2de3j2mqba4wgeuiaqz2k7syrm
      array-includes: 3.1.9
      array.prototype.findlastindex: 1.2.6
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 7.32.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.1_lo3sk5lgzxlcb5aoezcnaiw6za
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /eslint-plugin-jsx-a11y/6.10.2_eslint@7.32.0:
    resolution: {integrity: sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
    dependencies:
      aria-query: 5.3.2
      array-includes: 3.1.9
      array.prototype.flatmap: 1.3.3
      ast-types-flow: 0.0.8
      axe-core: 4.10.3
      axobject-query: 4.1.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      eslint: 7.32.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.1.0
      string.prototype.includes: 2.0.1
    dev: true

  /eslint-plugin-prettier/3.4.1_6l26irxuevddeh5uhyzqivbl64:
    resolution: {integrity: sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      eslint: '>=5.0.0'
      eslint-config-prettier: '*'
      prettier: '>=1.13.0'
    peerDependenciesMeta:
      eslint-config-prettier:
        optional: true
    dependencies:
      eslint: 7.32.0
      eslint-config-prettier: 6.15.0_eslint@7.32.0
      prettier: 2.8.8
      prettier-linter-helpers: 1.0.0
    dev: true

  /eslint-plugin-react-hooks/4.6.2_eslint@7.32.0:
    resolution: {integrity: sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
    dependencies:
      eslint: 7.32.0
    dev: true

  /eslint-plugin-react/7.37.5_eslint@7.32.0:
    resolution: {integrity: sha512-Qteup0SqU15kdocexFNAJMvCJEfa2xUKNV4CC1xsVMrIIqEy3SQ/rqyxCWNzfrd3/ldy6HMlD2e0JDVpDg2qIA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
    dependencies:
      array-includes: 3.1.9
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 7.32.0
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0
    dev: true

  /eslint-scope/5.1.1:
    resolution: {integrity: sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  /eslint-utils/2.1.0:
    resolution: {integrity: sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-visitor-keys/1.3.0:
    resolution: {integrity: sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=}
    engines: {node: '>=4'}
    dev: true

  /eslint-visitor-keys/2.1.0:
    resolution: {integrity: sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=}
    engines: {node: '>=10'}
    dev: true

  /eslint/7.32.0:
    resolution: {integrity: sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=}
    engines: {node: ^10.12.0 || >=12.0.0}
    hasBin: true
    dependencies:
      '@babel/code-frame': 7.12.11
      '@eslint/eslintrc': 0.4.3
      '@humanwhocodes/config-array': 0.5.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      doctrine: 3.0.0
      enquirer: 2.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
      eslint-visitor-keys: 2.1.0
      espree: 7.3.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 13.24.0
      ignore: 4.0.6
      import-fresh: 3.3.1
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      progress: 2.0.3
      regexpp: 3.2.0
      semver: 7.7.2
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      table: 6.9.0
      text-table: 0.2.0
      v8-compile-cache: 2.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree/7.3.1:
    resolution: {integrity: sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2_acorn@7.4.1
      eslint-visitor-keys: 1.3.0
    dev: true

  /esprima/4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /esquery/1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0

  /estraverse/4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  /estraverse/5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /etag/1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=}
    engines: {node: '>= 0.6'}

  /eventemitter3/4.0.7:
    resolution: {integrity: sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=}

  /events/3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=}
    engines: {node: '>=0.8.x'}

  /execa/4.1.0:
    resolution: {integrity: sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /execa/5.1.1:
    resolution: {integrity: sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  /expand-tilde/2.0.2:
    resolution: {integrity: sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      homedir-polyfill: 1.0.3
    dev: true

  /express/4.21.2:
    resolution: {integrity: sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  /extend-shallow/2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: true

  /extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    dev: true

  /external-editor/3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: true

  /external-remotes-plugin/1.0.0_webpack@5.100.1:
    resolution: {integrity: sha512-19cfGfzmPMXkRNpyqOHgndNB07vjk1G1V7IuyixBw8Z1QTc/IOhINy9TbWzDRPTmj64e3STTI1MJE0gI58p7YA==}
    peerDependencies:
      webpack: ^5
    dependencies:
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-sources: 2.3.1
    dev: true

  /extsprintf/1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=}
    engines: {'0': node >=0.6.0}
    dev: true

  /fancy-log/1.3.3:
    resolution: {integrity: sha512-k9oEhlyc0FrVh25qYuSELjr8oxsCoc4/LEZfg2iJJrfEk/tZL9bCoJE47gqAvI2m/AUjluCS4+3I0eTx8n3AEw==}
    engines: {node: '>= 0.10'}
    dependencies:
      ansi-gray: 0.1.1
      color-support: 1.1.3
      parse-node-version: 1.0.1
      time-stamp: 1.1.0
    dev: true

  /fast-deep-equal/2.0.1:
    resolution: {integrity: sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w==}
    dev: false

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=}

  /fast-diff/1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}
    dev: true

  /fast-fifo/1.3.2:
    resolution: {integrity: sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ==}
    dev: true

  /fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: true

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}
    dev: true

  /fast-uri/3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}

  /fast-xml-parser/4.2.5:
    resolution: {integrity: sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==}
    hasBin: true
    dependencies:
      strnum: 1.1.2
    dev: true

  /fastest-levenshtein/1.0.16:
    resolution: {integrity: sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==}
    engines: {node: '>= 4.9.1'}

  /fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}
    dependencies:
      reusify: 1.1.0
    dev: true

  /faye-websocket/0.11.4:
    resolution: {integrity: sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=}
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: 0.7.4

  /fbjs-css-vars/1.0.2:
    resolution: {integrity: sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ==}
    dev: false

  /fbjs/3.0.5:
    resolution: {integrity: sha512-ztsSx77JBtkuMrEypfhgc3cI0+0h+svqeie7xHbh1k/IKdcydnvadp/mUaGgjAOXQmQSxsqgaRhS3q9fy+1kxg==}
    dependencies:
      cross-fetch: 3.2.0
      fbjs-css-vars: 1.0.2
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 1.0.40
    transitivePeerDependencies:
      - encoding
    dev: false

  /fecha/4.2.3:
    resolution: {integrity: sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==}
    dev: true

  /figgy-pudding/3.5.2:
    resolution: {integrity: sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=}
    dev: true

  /figures/1.7.0:
    resolution: {integrity: sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      escape-string-regexp: 1.0.5
      object-assign: 4.1.1
    dev: true

  /figures/2.0.0:
    resolution: {integrity: sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=}
    engines: {node: '>=4'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /figures/3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /file-entry-cache/6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.2.0
    dev: true

  /file-loader/5.1.0_webpack@5.100.1:
    resolution: {integrity: sha512-u/VkLGskw3Ue59nyOwUwXI/6nuBCo7KBkniB/l7ICwr/7cPNGsL1WCXUp3GB0qgOOKU1TiP49bv4DZF/LJqprg==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 1.4.2
      schema-utils: 2.7.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /file-stream-rotator/0.6.1:
    resolution: {integrity: sha512-u+dBid4PvZw17PmDeRcNOtCP9CCK/9lRN2w+r1xIS7yOL9JFrIBKTvrYsxT4P0pGtThYTn++QS5ChHaUov3+zQ==}
    dependencies:
      moment: 2.30.1
    dev: true

  /fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /filter-obj/1.1.0:
    resolution: {integrity: sha1-mzERErxsYSehbgFsbF1/GeCAXFs=}
    engines: {node: '>=0.10.0'}
    dev: true

  /finalhandler/1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  /find-cache-dir/2.1.0:
    resolution: {integrity: sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0
    dev: true

  /find-cache-dir/3.3.2:
    resolution: {integrity: sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: true

  /find-up/2.1.0:
    resolution: {integrity: sha1-RdG35QbHF93UgndaK3eSCjwMV6c=}
    engines: {node: '>=4'}
    dependencies:
      locate-path: 2.0.0
    dev: true

  /find-up/3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: true

  /find-up/4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  /find-up/5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /findup-sync/5.0.0:
    resolution: {integrity: sha1-VDgK2WWn7coAzI9jETVZqtxUG9I=}
    engines: {node: '>= 10.13.0'}
    dependencies:
      detect-file: 1.0.0
      is-glob: 4.0.3
      micromatch: 4.0.8
      resolve-dir: 1.0.1
    dev: true

  /fined/2.0.0:
    resolution: {integrity: sha512-OFRzsL6ZMHz5s0JrsEr+TpdGNCtrVtnuG3x1yzGNiQHT0yaDnXAj8V/lWcpJVrnoDpcwXcASxAZYbuXda2Y82A==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      expand-tilde: 2.0.2
      is-plain-object: 5.0.0
      object.defaults: 1.1.0
      object.pick: 1.3.0
      parse-filepath: 1.0.2
    dev: true

  /flagged-respawn/2.0.0:
    resolution: {integrity: sha512-Gq/a6YCi8zexmGHMuJwahTGzXlAZAOsbCVKduWXC6TlLCjjFRlExMJc4GC2NYPYZ0r/brw9P7CpRgQmlPVeOoA==}
    engines: {node: '>= 10.13.0'}
    dev: true

  /flat-cache/3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
      rimraf: 3.0.2
    dev: true

  /flat/5.0.2:
    resolution: {integrity: sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=}
    hasBin: true

  /flatted/3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}
    dev: true

  /flush-write-stream/1.1.1:
    resolution: {integrity: sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /fn.name/1.1.0:
    resolution: {integrity: sha512-GRnmB5gPyJpAhTQdSZTSp9uaPSvl09KoYcMQtsB9rQoOmzs9dH6ffeccH+Z+cv6P68Hu5bC6JjRh4Ah/mHSNRw==}
    dev: true

  /follow-redirects/1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  /for-each/0.3.5:
    resolution: {integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
    dev: true

  /for-in/1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /for-own/1.0.0:
    resolution: {integrity: sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
    dev: true

  /foreground-child/3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0
    dev: true

  /forever-agent/0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=}
    dev: true

  /form-data/2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: true

  /formstream/1.5.1:
    resolution: {integrity: sha512-q7ORzFqotpwn3Y/GBK2lK7PjtZZwJHz9QE9Phv8zb5IrL9ftGLyi2zjGURON3voK8TaZ+mqJKERYN4lrHYTkUQ==}
    dependencies:
      destroy: 1.2.0
      mime: 2.6.0
      node-hex: 1.0.1
      pause-stream: 0.0.11
    dev: true

  /forwarded/0.2.0:
    resolution: {integrity: sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=}
    engines: {node: '>= 0.6'}

  /fresh/0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=}
    engines: {node: '>= 0.6'}

  /from2/2.3.0:
    resolution: {integrity: sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /fs-extra/9.1.0:
    resolution: {integrity: sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=}
    engines: {node: '>=10'}
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fs-minipass/1.2.7:
    resolution: {integrity: sha512-GWSSJGFy4e9GUeCcbIkED+bgAoFyj7XF1mV8rma3QW4NIqX9Kyx79N/PF61H5udOV3aY1IaMLs6pGbH71nlCTA==}
    dependencies:
      minipass: 2.9.0
    dev: true

  /fs-minipass/2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /fs-monkey/1.0.6:
    resolution: {integrity: sha512-b1FMfwetIKymC0eioW7mTywihSQE4oLzQn1dB6rZB5fx/3NpNEdAWeCSMB+60/AeT0TCXsxzAlcYVEFCTAksWg==}

  /fs-readdir-recursive/1.1.0:
    resolution: {integrity: sha512-GNanXlVr2pf02+sPN40XN8HG+ePaNcvM0q5mZBd668Obwb0yD5GiUbZOFgwn8kGMY6I3mdyDJzieUy3PTYyTRA==}
    dev: true

  /fs-write-stream-atomic/1.0.10:
    resolution: {integrity: sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=}
    dependencies:
      graceful-fs: 4.2.11
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.8
    dev: true

  /fs.realpath/1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}

  /fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind/1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  /function.prototype.name/1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7
    dev: true

  /functional-red-black-tree/1.0.1:
    resolution: {integrity: sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=}
    dev: true

  /functions-have-names/1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: true

  /gauge/2.7.4:
    resolution: {integrity: sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=}
    dependencies:
      aproba: 1.2.0
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 1.0.2
      strip-ansi: 3.0.1
      wide-align: 1.1.5
    dev: true

  /generic-pool/3.8.2:
    resolution: {integrity: sha512-nGToKy6p3PAbYQ7p1UlWl6vSPwfwU6TMSWK7TTu+WUY4ZjyZQGniGGt2oNVvyNSpyZYSB43zMXVLcBm08MTMkg==}
    engines: {node: '>= 4'}
    dev: true

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=}
    engines: {node: '>=6.9.0'}
    dev: true

  /get-caller-file/2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  /get-east-asian-width/1.3.0:
    resolution: {integrity: sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==}
    engines: {node: '>=18'}
    dev: true

  /get-intrinsic/1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  /get-own-enumerable-property-symbols/3.0.2:
    resolution: {integrity: sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g==}
    dev: true

  /get-pkg-repo/4.2.1:
    resolution: {integrity: sha1-dZc+HIBQxz9IGQxSBHxM7jrL84U=}
    engines: {node: '>=6.9.0'}
    hasBin: true
    dependencies:
      '@hutson/parse-repository-url': 3.0.2
      hosted-git-info: 4.1.0
      through2: 2.0.5
      yargs: 16.2.0
    dev: true

  /get-port/5.1.1:
    resolution: {integrity: sha1-BGntB1Y0ed5u+5hrrwU9zX1OMZM=}
    engines: {node: '>=8'}
    dev: true

  /get-proto/1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  /get-ready/1.0.0:
    resolution: {integrity: sha1-+RgX8emt7P6hOlYq38jeiDqzR4I=}
    dev: true

  /get-stdin/6.0.0:
    resolution: {integrity: sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g==}
    engines: {node: '>=4'}
    dev: true

  /get-stream/5.2.0:
    resolution: {integrity: sha1-SWaheV7lrOZecGxLe+txJX1uItM=}
    engines: {node: '>=8'}
    dependencies:
      pump: 3.0.3
    dev: true

  /get-stream/6.0.1:
    resolution: {integrity: sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=}
    engines: {node: '>=10'}

  /get-symbol-description/1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
    dev: true

  /get-them-args/1.3.2:
    resolution: {integrity: sha512-LRn8Jlk+DwZE4GTlDbT3Hikd1wSHgLMme/+7ddlqKd7ldwR6LjJgTVWzBnR01wnYGe4KgrXjg287RaI22UHmAw==}
    dev: true

  /getpass/0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=}
    dependencies:
      assert-plus: 1.0.0
    dev: true

  /git-raw-commits/2.0.11:
    resolution: {integrity: sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      dargs: 7.0.0
      lodash: 4.17.21
      meow: 8.1.2
      split2: 3.2.2
      through2: 4.0.2
    dev: true

  /git-remote-origin-url/2.0.0:
    resolution: {integrity: sha1-UoJlna4hBxRaERJhEq0yFuxfpl8=}
    engines: {node: '>=4'}
    dependencies:
      gitconfiglocal: 1.0.0
      pify: 2.3.0
    dev: true

  /git-semver-tags/4.1.1:
    resolution: {integrity: sha1-YxkbzYCbDsPhUbpHUcFsRE5bV4A=}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      meow: 8.1.2
      semver: 6.3.1
    dev: true

  /git-up/4.0.5:
    resolution: {integrity: sha1-57twmBo36i+4/gSWaYAKH5oB11k=}
    dependencies:
      is-ssh: 1.4.1
      parse-url: 6.0.5
    dev: true

  /git-url-parse/11.6.0:
    resolution: {integrity: sha1-xjS43n+qZkmKK4iTLfMXAsZ99gU=}
    dependencies:
      git-up: 4.0.5
    dev: true

  /gitconfiglocal/1.0.0:
    resolution: {integrity: sha1-QdBF84UaXqiPA/JMocYXgRRGS5s=}
    dependencies:
      ini: 1.3.8
    dev: true

  /glob-parent/3.1.0:
    resolution: {integrity: sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=}
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-to-regexp/0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  /glob/11.0.3:
    resolution: {integrity: sha512-2Nim7dha1KVkaiF4q6Dj+ngPPMdfvLJEOpZk/jKiUAkqKebpGAWQXAq9z1xu9HKu5lWfqw/FASuccEjyznjPaA==}
    engines: {node: 20 || >=22}
    hasBin: true
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 4.1.1
      minimatch: 10.0.3
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 2.0.0
    dev: true

  /glob/7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /glob/8.0.3:
    resolution: {integrity: sha512-ull455NHSHI/Y1FqGaaYFaLGkNMMJbavMrEGFXG/PGrg6y7sutWHUHrz6gy6WEBH6akM1M414dWKCNs+IhKdiQ==}
    engines: {node: '>=12'}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0
    dev: true

  /global-modules/1.0.0:
    resolution: {integrity: sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      global-prefix: 1.0.2
      is-windows: 1.0.2
      resolve-dir: 1.0.1
    dev: true

  /global-prefix/1.0.2:
    resolution: {integrity: sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: 2.0.2
      homedir-polyfill: 1.0.3
      ini: 1.3.8
      is-windows: 1.0.2
      which: 1.3.1
    dev: true

  /globals/13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globalthis/1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0
    dev: true

  /globby/11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /globby/13.2.2:
    resolution: {integrity: sha512-Y1zNGV+pzQdh7H39l9zgB4PJqjRNqydvdYCDG4HFXM4XuvSaQQlEc91IU1yALL8gUTDomgBAfz3XJdmUS+oo0w==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 4.0.0
    dev: true

  /globby/6.1.0:
    resolution: {integrity: sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-union: 1.0.2
      glob: 7.2.3
      object-assign: 4.1.1
      pify: 2.3.0
      pinkie-promise: 2.0.1
    dev: true

  /globby/7.1.1:
    resolution: {integrity: sha1-+yzP+UAfhgCUXfral0QMypcrhoA=}
    engines: {node: '>=4'}
    dependencies:
      array-union: 1.0.2
      dir-glob: 2.2.2
      glob: 7.2.3
      ignore: 3.3.10
      pify: 3.0.0
      slash: 1.0.0
    dev: true

  /google-libphonenumber/3.2.42:
    resolution: {integrity: sha512-60jm6Lu72WmlUJXUBJmmuZlHG2vDJ2gQ9pL5gcFsSe1Q4eigsm0Z1ayNHjMgqGUl0zey8JqKtO4QCHPV+5LCNQ==}
    engines: {node: '>=0.10'}
    dev: false

  /gopd/1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  /got/11.8.6:
    resolution: {integrity: sha512-6tfZ91bOr7bOXnK7PRDCGBLa1H4U080YHNaAQ2KsMGlLEzRbk44nsZF2E1IeRc3vtJHPVbKCYgdFbaGO2ljd8g==}
    engines: {node: '>=10.19.0'}
    dependencies:
      '@sindresorhus/is': 4.6.0
      '@szmarczak/http-timer': 4.0.6
      '@types/cacheable-request': 6.0.3
      '@types/responselike': 1.0.3
      cacheable-lookup: 5.0.4
      cacheable-request: 7.0.4
      decompress-response: 6.0.0
      http2-wrapper: 1.0.3
      lowercase-keys: 2.0.0
      p-cancelable: 2.1.1
      responselike: 2.0.1
    dev: true

  /graceful-fs/4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  /handle-thing/2.0.1:
    resolution: {integrity: sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=}

  /handlebars/4.7.8:
    resolution: {integrity: sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==}
    engines: {node: '>=0.4.7'}
    hasBin: true
    dependencies:
      minimist: 1.2.8
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.19.3
    dev: true

  /har-schema/2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=}
    engines: {node: '>=4'}
    dev: true

  /har-validator/5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0
    dev: true

  /hard-rejection/2.1.0:
    resolution: {integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==}
    engines: {node: '>=6'}
    dev: true

  /has-ansi/2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /has-bigints/1.1.0:
    resolution: {integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==}
    engines: {node: '>= 0.4'}
    dev: true

  /has-flag/3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  /has-property-descriptors/1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.1
    dev: true

  /has-proto/1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
    dev: true

  /has-symbols/1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  /has-tostringtag/1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0
    dev: true

  /has-unicode/2.0.1:
    resolution: {integrity: sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=}
    dev: true

  /hasown/2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: true

  /header-case/2.0.4:
    resolution: {integrity: sha1-WkLmO1UXc0nPQFvrjXdayruSwGM=}
    dependencies:
      capital-case: 1.0.4
      tslib: 2.8.1
    dev: true

  /highlight.js/9.18.4:
    resolution: {integrity: sha1-4/jfT171/1a7v0zVNTkaMxIOSDU=}
    requiresBuild: true
    dev: true

  /history/4.10.1:
    resolution: {integrity: sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew==}
    dependencies:
      '@babel/runtime': 7.27.6
      loose-envify: 1.4.0
      resolve-pathname: 3.0.0
      tiny-invariant: 1.3.3
      tiny-warning: 1.0.3
      value-equal: 1.0.1
    dev: false

  /hoist-non-react-statics/3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1
    dev: false

  /homedir-polyfill/1.0.3:
    resolution: {integrity: sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      parse-passwd: 1.0.0
    dev: true

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=}
    dev: true

  /hosted-git-info/4.1.0:
    resolution: {integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==}
    engines: {node: '>=10'}
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /hpack.js/2.1.6:
    resolution: {integrity: sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=}
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  /html-entities/2.6.0:
    resolution: {integrity: sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==}

  /html-minifier-terser/6.1.0:
    resolution: {integrity: sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=}
    engines: {node: '>=12'}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.43.1
    dev: true

  /html-webpack-plugin/5.6.3_webpack@5.100.1:
    resolution: {integrity: sha512-QSf1yjtSAsmf7rYBV7XX86uua4W/vkhIt0xNXKbsi2foEeW7vjJQz4bhnpL3xH+l1ryl1680uNv968Z+X6jSYg==}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.20.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.2
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /htmlparser2/6.1.0:
    resolution: {integrity: sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0
    dev: true

  /http-assert/1.5.0:
    resolution: {integrity: sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==}
    engines: {node: '>= 0.8'}
    dependencies:
      deep-equal: 1.0.1
      http-errors: 1.8.1
    dev: true

  /http-cache-semantics/4.2.0:
    resolution: {integrity: sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==}
    dev: true

  /http-deceiver/1.2.7:
    resolution: {integrity: sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=}

  /http-errors/1.6.3:
    resolution: {integrity: sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  /http-errors/1.8.1:
    resolution: {integrity: sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 1.5.0
      toidentifier: 1.0.1
    dev: true

  /http-errors/2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  /http-parser-js/0.5.10:
    resolution: {integrity: sha512-Pysuw9XpUq5dVc/2SMHpuTY01RFl8fttgcyunjL7eEMhGM3cI4eOmiCycJDVCo/7O7ClfQD3SaI6ftDzqOXYMA==}

  /http-proxy-agent/4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /http-proxy-middleware/2.0.9:
    resolution: {integrity: sha512-c1IyJYLYppU574+YI7R4QyX2ystMtVXZwIdzazUIPIJsHuWNd+mho2j+bKoHftndicGj9yh+xjd+l0yj7VeT1Q==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true
    dependencies:
      '@types/http-proxy': 1.17.16
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    transitivePeerDependencies:
      - debug
    dev: true

  /http-proxy-middleware/2.0.9_@types+express@4.17.23:
    resolution: {integrity: sha512-c1IyJYLYppU574+YI7R4QyX2ystMtVXZwIdzazUIPIJsHuWNd+mho2j+bKoHftndicGj9yh+xjd+l0yj7VeT1Q==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true
    dependencies:
      '@types/express': 4.17.23
      '@types/http-proxy': 1.17.16
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    transitivePeerDependencies:
      - debug

  /http-proxy/1.18.1:
    resolution: {integrity: sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  /http-signature/1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0
    dev: true

  /http2-wrapper/1.0.3:
    resolution: {integrity: sha1-uPVeDB8l1OvQizsMLAeflZCACz0=}
    engines: {node: '>=10.19.0'}
    dependencies:
      quick-lru: 5.1.1
      resolve-alpn: 1.2.1
    dev: true

  /https-proxy-agent/5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /human-signals/1.1.1:
    resolution: {integrity: sha1-xbHNFPUK6uCatsWf5jujOV/k36M=}
    engines: {node: '>=8.12.0'}
    dev: true

  /human-signals/2.1.0:
    resolution: {integrity: sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=}
    engines: {node: '>=10.17.0'}

  /humanize-ms/1.2.1:
    resolution: {integrity: sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=}
    dependencies:
      ms: 2.1.3
    dev: true

  /husky/8.0.3:
    resolution: {integrity: sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /i18next/23.16.8:
    resolution: {integrity: sha512-06r/TitrM88Mg5FdUXAKL96dJMzgqLE5dv3ryBAra4KCwD9mJ4ndOTS95ZuymIGoE+2hzfdaMak2X11/es7ZWg==}
    dependencies:
      '@babel/runtime': 7.27.6
    dev: false

  /iconv-lite/0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2

  /iconv-lite/0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: true

  /icss-utils/4.1.1:
    resolution: {integrity: sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /ieee754/1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=}
    dev: true

  /iferr/0.1.5:
    resolution: {integrity: sha1-xg7taebY/bazEEofy8ocGS3FtQE=}
    dev: true

  /ignore-walk/3.0.4:
    resolution: {integrity: sha1-yaCfabfHtHml10rBo8DUI20qYzU=}
    dependencies:
      minimatch: 3.1.2
    dev: true

  /ignore/3.3.10:
    resolution: {integrity: sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==}
    dev: true

  /ignore/4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}
    dev: true

  /ignore/5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}
    dev: true

  /import-fresh/3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /import-local/3.2.0:
    resolution: {integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  /imurmurhash/0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string/3.2.0:
    resolution: {integrity: sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=}
    engines: {node: '>=4'}
    dev: true

  /indent-string/4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: true

  /indent-string/5.0.0:
    resolution: {integrity: sha1-T9KYD8yvhiLRTGTWlPTPM8gZUaU=}
    engines: {node: '>=12'}
    dev: true

  /infer-owner/1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}
    dev: true

  /inflation/2.1.0:
    resolution: {integrity: sha512-t54PPJHG1Pp7VQvxyVCJ9mBbjG3Hqryges9bXoOO6GExCPa+//i/d5GSuFtpx3ALLd7lgIAur6zrIlBQyJuMlQ==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /inflight/1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits/2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=}

  /inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /ini/1.3.8:
    resolution: {integrity: sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=}
    dev: true

  /init-package-json/2.0.5:
    resolution: {integrity: sha1-eLhfPDYBTbQtjzIRclJQT2gCJkY=}
    engines: {node: '>=10'}
    dependencies:
      npm-package-arg: 8.1.5
      promzard: 0.3.0
      read: 1.0.7
      read-package-json: 4.1.2
      semver: 7.7.2
      validate-npm-package-license: 3.0.4
      validate-npm-package-name: 3.0.0
    dev: true

  /inquirer/7.3.3:
    resolution: {integrity: sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: true

  /inquirer/9.3.7:
    resolution: {integrity: sha512-LJKFHCSeIRq9hanN14IlOtPSTe3lNES7TYDTE2xxdAy1LS5rYphajK1qtwvj3YmQXvvk0U2Vbmcni8P9EIQW9w==}
    engines: {node: '>=18'}
    dependencies:
      '@inquirer/figures': 1.0.12
      ansi-escapes: 4.3.2
      cli-width: 4.1.0
      external-editor: 3.1.0
      mute-stream: 1.0.0
      ora: 5.4.1
      run-async: 3.0.0
      rxjs: 7.8.2
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0
      yoctocolors-cjs: 2.1.2
    dev: true

  /internal-slot/1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0
    dev: true

  /interpret/3.1.1:
    resolution: {integrity: sha512-6xwYfHbajpoF0xLW+iwLkhwgvLoZDfjYfoFNu8ftMoXINzwuymNLd9u/KmwtdT2GbR+/Cz66otEGEVVUHX9QLQ==}
    engines: {node: '>=10.13.0'}

  /ip-address/9.0.5:
    resolution: {integrity: sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==}
    engines: {node: '>= 12'}
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3
    dev: true

  /ip/1.1.9:
    resolution: {integrity: sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ==}
    dev: true

  /ipaddr.js/1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  /ipaddr.js/2.2.0:
    resolution: {integrity: sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==}
    engines: {node: '>= 10'}

  /is-absolute/1.0.0:
    resolution: {integrity: sha1-OV4a6EsR8mrReV5zwXN45IowFXY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-relative: 1.0.0
      is-windows: 1.0.2
    dev: true

  /is-arguments/1.2.0:
    resolution: {integrity: sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-array-buffer/3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
    dev: true

  /is-arrayish/0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}
    dev: true

  /is-arrayish/0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: true

  /is-async-function/2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0
    dev: true

  /is-bigint/1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-bigints: 1.1.0
    dev: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.3.0

  /is-boolean-object/1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-callable/1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-ci/2.0.0:
    resolution: {integrity: sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==}
    hasBin: true
    dependencies:
      ci-info: 2.0.0
    dev: true

  /is-class-hotfix/0.0.6:
    resolution: {integrity: sha512-0n+pzCC6ICtVr/WXnN2f03TK/3BfXY7me4cjCAqT8TYXEl0+JBRoqBo94JJHXcyDSLUeWbNX8Fvy5g5RJdAstQ==}
    dev: true

  /is-core-module/2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2

  /is-data-view/1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15
    dev: true

  /is-date-object/1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-docker/2.2.1:
    resolution: {integrity: sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=}
    engines: {node: '>=8'}
    hasBin: true

  /is-extendable/0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-extglob/2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}

  /is-finalizationregistry/1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
    dev: true

  /is-fullwidth-code-point/1.0.0:
    resolution: {integrity: sha1-754xOG8DGn8NZDr4L95QxFfvAMs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      number-is-nan: 1.0.1
    dev: true

  /is-fullwidth-code-point/2.0.0:
    resolution: {integrity: sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=}
    engines: {node: '>=4'}
    dev: true

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-generator-function/1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0
    dev: true

  /is-glob/3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-interactive/1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}
    dev: true

  /is-interactive/2.0.0:
    resolution: {integrity: sha1-QMV2FFk4JtoRAK3mBZd41ZfxbpA=}
    engines: {node: '>=12'}
    dev: true

  /is-lambda/1.0.1:
    resolution: {integrity: sha1-PZh3iZ5qU+/AFgUEzeFfgubwYdU=}
    dev: true

  /is-map/2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-negative-zero/2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-number-object/1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-obj/1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-obj/2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}
    dev: true

  /is-observable/1.1.0:
    resolution: {integrity: sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=}
    engines: {node: '>=4'}
    dependencies:
      symbol-observable: 1.2.0
    dev: true

  /is-path-cwd/2.2.0:
    resolution: {integrity: sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==}
    engines: {node: '>=6'}
    dev: true

  /is-path-cwd/3.0.0:
    resolution: {integrity: sha1-iJtB5VyFiLHrKpamHQV0CmdFIcc=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  /is-path-in-cwd/2.1.0:
    resolution: {integrity: sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ==}
    engines: {node: '>=6'}
    dependencies:
      is-path-inside: 2.1.0
    dev: true

  /is-path-inside/2.1.0:
    resolution: {integrity: sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg==}
    engines: {node: '>=6'}
    dependencies:
      path-is-inside: 1.0.2
    dev: true

  /is-path-inside/3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}
    dev: true

  /is-path-inside/4.0.0:
    resolution: {integrity: sha1-gFrrYsR8GxL8P9E7+z7R50MAcds=}
    engines: {node: '>=12'}
    dev: true

  /is-plain-obj/1.1.0:
    resolution: {integrity: sha1-caUMhCnfync8kqOQpKA7OfzVHT4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-plain-obj/2.1.0:
    resolution: {integrity: sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj/3.0.0:
    resolution: {integrity: sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=}
    engines: {node: '>=10'}

  /is-plain-object/2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /is-plain-object/5.0.0:
    resolution: {integrity: sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-promise/2.2.2:
    resolution: {integrity: sha1-OauVnMv5p3TPB597QMeib3YxNfE=}
    dev: true

  /is-regex/1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: true

  /is-regexp/1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-relative/1.0.0:
    resolution: {integrity: sha1-obtpNc6MXboei5dUubLcwCDiJg0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-unc-path: 1.0.0
    dev: true

  /is-set/2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-shared-array-buffer/1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
    dev: true

  /is-ssh/1.4.1:
    resolution: {integrity: sha512-JNeu1wQsHjyHgn9NcWTaXq6zWSR6hqE0++zhfZlkFBbScNkyvxCdeV8sRkSBaeLKxmbpR21brail63ACNxJ0Tg==}
    dependencies:
      protocols: 2.0.2
    dev: true

  /is-stream/1.1.0:
    resolution: {integrity: sha1-EtSj3U5o4Lec6428hBc66A2RykQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-stream/2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=}
    engines: {node: '>=8'}

  /is-string/1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-symbol/1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0
    dev: true

  /is-text-path/1.0.1:
    resolution: {integrity: sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      text-extensions: 1.9.0
    dev: true

  /is-type-of/1.4.0:
    resolution: {integrity: sha512-EddYllaovi5ysMLMEN7yzHEKh8A850cZ7pykrY1aNRQGn/CDjRDE9qEWbIdt7xGEVJmjBXzU/fNnC4ABTm8tEQ==}
    dependencies:
      core-util-is: 1.0.3
      is-class-hotfix: 0.0.6
      isstream: 0.1.2
    dev: true

  /is-typed-array/1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.19
    dev: true

  /is-typedarray/1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=}
    dev: true

  /is-unc-path/1.0.0:
    resolution: {integrity: sha1-1zHoiY7QkKEsNSrS6u1Qla0yLJ0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      unc-path-regex: 0.1.2
    dev: true

  /is-unicode-supported/0.1.0:
    resolution: {integrity: sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=}
    engines: {node: '>=10'}
    dev: true

  /is-unicode-supported/1.3.0:
    resolution: {integrity: sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==}
    engines: {node: '>=12'}
    dev: true

  /is-unicode-supported/2.1.0:
    resolution: {integrity: sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ==}
    engines: {node: '>=18'}
    dev: true

  /is-weakmap/2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-weakref/1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
    dev: true

  /is-weakset/2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
    dev: true

  /is-windows/1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-wsl/2.2.0:
    resolution: {integrity: sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1

  /isarray/0.0.1:
    resolution: {integrity: sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=}
    dev: false

  /isarray/1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=}

  /isarray/2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}
    dev: true

  /isbinaryfile/5.0.4:
    resolution: {integrity: sha512-YKBKVkKhty7s8rxddb40oOkuP0NbaeXrQvLin6QMHL7Ypiy2RW9LwOVrVgZRyOrhQlayMd9t+D8yDy8MKFTSDQ==}
    engines: {node: '>= 18.0.0'}
    dev: true

  /isexe/2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}

  /isobject/3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=}
    engines: {node: '>=0.10.0'}

  /isstream/0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=}
    dev: true

  /iterator.prototype/1.1.5:
    resolution: {integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2
    dev: true

  /jackspeak/4.1.1:
    resolution: {integrity: sha512-zptv57P3GpL+O0I7VdMJNBZCu+BPHVQUk55Ft8/QCJjTVxrnJHuVuX/0Bl2A6/+2oyR/ZMEuFKwmzqqZ/U5nPQ==}
    engines: {node: 20 || >=22}
    dependencies:
      '@isaacs/cliui': 8.0.2
    dev: true

  /jest-worker/26.6.2:
    resolution: {integrity: sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 22.16.4
      merge-stream: 2.0.0
      supports-color: 7.2.0
    dev: true

  /jest-worker/27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 22.16.4
      merge-stream: 2.0.0
      supports-color: 8.1.1

  /js-base64/2.6.4:
    resolution: {integrity: sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=}
    dev: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml/3.14.1:
    resolution: {integrity: sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: true

  /jsbn/0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=}
    dev: true

  /jsbn/1.1.0:
    resolution: {integrity: sha1-sBMHyym2GKHtJux56RH4A8TaAEA=}
    dev: true

  /jsesc/3.0.2:
    resolution: {integrity: sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsesc/3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /json-buffer/3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=}
    dev: true

  /json-parse-better-errors/1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}
    dev: true

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=}

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: true

  /json-schema-traverse/1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=}

  /json-schema-typed/7.0.3:
    resolution: {integrity: sha1-I/9IG4tO680soSO0+gQJ5mRpotk=}
    dev: true

  /json-schema/0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=}
    dev: true

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}
    dev: true

  /json-stringify-safe/5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=}
    dev: true

  /json5/0.5.1:
    resolution: {integrity: sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=}
    hasBin: true
    dev: true

  /json5/1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /json5/2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsonfile/6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=}
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsonparse/1.3.1:
    resolution: {integrity: sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=}
    engines: {'0': node >= 0.2.0}
    dev: true

  /jsprim/1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0
    dev: true

  /jstoxml/0.2.4:
    resolution: {integrity: sha1-/z+2eFaIOgMpU8fOjOdIYhD0hEc=}
    engines: {node: '>=0.2.0'}
    dev: true

  /jstoxml/2.2.9:
    resolution: {integrity: sha512-OYWlK0j+roh+eyaMROlNbS5cd5R25Y+IUpdl7cNdB8HNrkgwQzIS7L9MegxOiWNBj9dQhA/yAxiMwCC5mwNoBw==}
    dev: true

  /jsx-ast-utils/3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}
    dependencies:
      array-includes: 3.1.9
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1
    dev: true

  /keygrip/1.1.0:
    resolution: {integrity: sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      tsscmp: 1.0.6
    dev: true

  /keyv/4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}
    dependencies:
      json-buffer: 3.0.1
    dev: true

  /kill-port/1.6.1:
    resolution: {integrity: sha512-un0Y55cOM7JKGaLnGja28T38tDDop0AQ8N0KlAdyh+B1nmMoX8AnNmqPNZbS3mUMgiST51DCVqmbFT1gNJpVNw==}
    hasBin: true
    dependencies:
      get-them-args: 1.3.2
      shell-exec: 1.0.2
    dev: true

  /kind-of/6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  /ko-sleep/1.1.4:
    resolution: {integrity: sha1-VkYvuoNeB7uMJs+gg/mJOj/eVGk=}
    dependencies:
      ms: 2.1.3
    dev: true

  /koa-bodyparser/4.4.1:
    resolution: {integrity: sha512-kBH3IYPMb+iAXnrxIhXnW+gXV8OTzCu8VPDqvcDHW9SQrbkHmqPQtiZwrltNmSq6/lpipHnT7k7PsjlVD7kK0w==}
    engines: {node: '>=8.0.0'}
    dependencies:
      co-body: 6.2.0
      copy-to: 2.0.1
      type-is: 1.6.18
    dev: true

  /koa-compose/4.1.0:
    resolution: {integrity: sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==}
    dev: true

  /koa-convert/2.0.0:
    resolution: {integrity: sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==}
    engines: {node: '>= 10'}
    dependencies:
      co: 4.6.0
      koa-compose: 4.1.0
    dev: true

  /koa-router/10.1.1:
    resolution: {integrity: sha512-z/OzxVjf5NyuNO3t9nJpx7e1oR3FSBAauiwXtMQu4ppcnuNZzTaQ4p21P8A6r2Es8uJJM339oc4oVW+qX7SqnQ==}
    engines: {node: '>= 8.0.0'}
    deprecated: Please use @koa/router instead of koa-router. This is the same package maintained by the same people, but under the Koa team's control on GitHub. Versioning is identical and it is a drop-in replacement. Maintenance is supported by Forward Email @ https://forwardemail.net
    dependencies:
      debug: 4.4.1
      http-errors: 1.8.1
      koa-compose: 4.1.0
      methods: 1.1.2
      path-to-regexp: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /koa-send/5.0.1:
    resolution: {integrity: sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==}
    engines: {node: '>= 8'}
    dependencies:
      debug: 4.4.1
      http-errors: 1.8.1
      resolve-path: 1.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /koa-static-cache/5.1.4:
    resolution: {integrity: sha512-abVWOHY6z6qSTvNtapWMAnvHS9SUiUCaQQQubClSAT9ybQPsZ6ioKcRarnownS4fMD0sXQgQ5ey8CYEuwoa1Yg==}
    engines: {node: '>= 7.6.0'}
    dependencies:
      compressible: 2.0.18
      debug: 3.2.7
      fs-readdir-recursive: 1.1.0
      mime-types: 2.1.35
      mz: 2.7.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /koa-static/5.0.0:
    resolution: {integrity: sha512-UqyYyH5YEXaJrf9S8E23GoJFQZXkBVJ9zYYMPGz919MSX1KuvAcycIuS0ci150HCoPf4XQVhQ84Qf8xRPWxFaQ==}
    engines: {node: '>= 7.6.0'}
    dependencies:
      debug: 3.2.7
      koa-send: 5.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /koa/2.16.1:
    resolution: {integrity: sha512-umfX9d3iuSxTQP4pnzLOz0HKnPg0FaUUIKcye2lOiz3KPu1Y3M3xlz76dISdFPQs37P9eJz1wUpcTS6KDPn9fA==}
    engines: {node: ^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4}
    dependencies:
      accepts: 1.3.8
      cache-content-type: 1.0.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookies: 0.9.1
      debug: 4.4.1
      delegates: 1.0.0
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      fresh: 0.5.2
      http-assert: 1.5.0
      http-errors: 1.8.1
      is-generator-function: 1.1.0
      koa-compose: 4.1.0
      koa-convert: 2.0.0
      on-finished: 2.4.1
      only: 0.0.2
      parseurl: 1.3.3
      statuses: 1.5.0
      type-is: 1.6.18
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /kuler/2.0.0:
    resolution: {integrity: sha512-Xq9nH7KlWZmXAtodXDDRE7vs6DU1gTU8zYDHDiWLSip45Egwq3plLHzPn27NgvzL2r1LMPC1vdqh98sQxtqj4A==}
    dev: true

  /language-subtag-registry/0.3.23:
    resolution: {integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==}
    dev: true

  /language-tags/1.0.9:
    resolution: {integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==}
    engines: {node: '>=0.10'}
    dependencies:
      language-subtag-registry: 0.3.23
    dev: true

  /launch-editor/2.10.0:
    resolution: {integrity: sha512-D7dBRJo/qcGX9xlvt/6wUYzQxjh5G1RvZPgPv8vi4KRU99DVQL/oW7tnVOCCTm2HGeo3C5HvGE5Yrh6UBoZ0vA==}
    dependencies:
      picocolors: 1.1.1
      shell-quote: 1.8.3

  /lazystream/1.0.1:
    resolution: {integrity: sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=}
    engines: {node: '>= 0.6.3'}
    dependencies:
      readable-stream: 2.3.8
    dev: true

  /lerna/4.0.0:
    resolution: {integrity: sha1-sTnWhdUOoMob6HcTp8L0Sltnjp4=}
    engines: {node: '>= 10.18.0'}
    hasBin: true
    dependencies:
      '@lerna/add': 4.0.0
      '@lerna/bootstrap': 4.0.0
      '@lerna/changed': 4.0.0
      '@lerna/clean': 4.0.0
      '@lerna/cli': 4.0.0
      '@lerna/create': 4.0.0
      '@lerna/diff': 4.0.0
      '@lerna/exec': 4.0.0
      '@lerna/import': 4.0.0
      '@lerna/info': 4.0.0
      '@lerna/init': 4.0.0
      '@lerna/link': 4.0.0
      '@lerna/list': 4.0.0
      '@lerna/publish': 4.0.0
      '@lerna/run': 4.0.0
      '@lerna/version': 4.0.0
      import-local: 3.2.0
      npmlog: 4.1.2
    transitivePeerDependencies:
      - bluebird
      - encoding
      - supports-color
    dev: true

  /levn/0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /libnpmaccess/4.0.3:
    resolution: {integrity: sha1-37DlsKU8MVomENMA5GtN3rZufuw=}
    engines: {node: '>=10'}
    dependencies:
      aproba: 2.1.0
      minipass: 3.3.6
      npm-package-arg: 8.1.5
      npm-registry-fetch: 11.0.0
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /libnpmpublish/4.0.2:
    resolution: {integrity: sha1-vnfov1lWExvLRePKprlqhC3sB5Q=}
    engines: {node: '>=10'}
    dependencies:
      normalize-package-data: 3.0.3
      npm-package-arg: 8.1.5
      npm-registry-fetch: 11.0.0
      semver: 7.7.2
      ssri: 8.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /liftoff/4.0.0:
    resolution: {integrity: sha512-rMGwYF8q7g2XhG2ulBmmJgWv25qBsqRbDn5gH0+wnuyeFt7QBJlHJmtg5qEdn4pN6WVAUMgXnIxytMFRX9c1aA==}
    engines: {node: '>=10.13.0'}
    dependencies:
      extend: 3.0.2
      findup-sync: 5.0.0
      fined: 2.0.0
      flagged-respawn: 2.0.0
      is-plain-object: 5.0.0
      object.map: 1.0.1
      rechoir: 0.8.0
      resolve: 1.22.10
    dev: true

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}
    dev: true

  /linkify-it/3.0.3:
    resolution: {integrity: sha1-qYuvRM5FpVDvtNScdp0HUkzC+i4=}
    dependencies:
      uc.micro: 1.0.6
    dev: false

  /lint-staged/10.5.4:
    resolution: {integrity: sha1-zRU7XwmH0jcfwdKEekCaL+cFtmU=}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      cli-truncate: 2.1.0
      commander: 6.2.1
      cosmiconfig: 7.1.0
      debug: 4.4.1
      dedent: 0.7.0
      enquirer: 2.4.1
      execa: 4.1.0
      listr2: 3.14.0_enquirer@2.4.1
      log-symbols: 4.1.0
      micromatch: 4.0.8
      normalize-path: 3.0.0
      please-upgrade-node: 3.2.0
      string-argv: 0.3.1
      stringify-object: 3.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /listr-silent-renderer/1.1.1:
    resolution: {integrity: sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=}
    engines: {node: '>=4'}
    dev: true

  /listr-update-renderer/0.5.0_listr@0.14.3:
    resolution: {integrity: sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=}
    engines: {node: '>=6'}
    peerDependencies:
      listr: ^0.14.2
    dependencies:
      chalk: 1.1.3
      cli-truncate: 0.2.1
      elegant-spinner: 1.0.1
      figures: 1.7.0
      indent-string: 3.2.0
      listr: 0.14.3
      log-symbols: 1.0.2
      log-update: 2.3.0
      strip-ansi: 3.0.1
    dev: true

  /listr-verbose-renderer/0.5.0:
    resolution: {integrity: sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      date-fns: 1.30.1
      figures: 2.0.0
    dev: true

  /listr/0.14.3:
    resolution: {integrity: sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=}
    engines: {node: '>=6'}
    dependencies:
      '@samverschueren/stream-to-observable': 0.3.1_rxjs@6.6.7
      is-observable: 1.1.0
      is-promise: 2.2.2
      is-stream: 1.1.0
      listr-silent-renderer: 1.1.1
      listr-update-renderer: 0.5.0_listr@0.14.3
      listr-verbose-renderer: 0.5.0
      p-map: 2.1.0
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zen-observable
      - zenObservable
    dev: true

  /listr2/3.14.0_enquirer@2.4.1:
    resolution: {integrity: sha512-TyWI8G99GX9GjE54cJ+RrNMcIFBfwMPxc3XTFiAYGN4s10hWROGtOg7+O6u6LE3mNkyld7RSLE6nrKBvTfcs3g==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true
    dependencies:
      cli-truncate: 2.1.0
      colorette: 2.0.20
      enquirer: 2.4.1
      log-update: 4.0.0
      p-map: 4.0.0
      rfdc: 1.4.1
      rxjs: 7.8.2
      through: 2.3.8
      wrap-ansi: 7.0.0
    dev: true

  /load-json-file/4.0.0:
    resolution: {integrity: sha1-L19Fq5HjMhYjT9U62rZo607AmTs=}
    engines: {node: '>=4'}
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0
    dev: true

  /load-json-file/6.2.0:
    resolution: {integrity: sha512-gUD/epcRms75Cw8RT1pUdHugZYM5ce64ucs2GEISABwkRsOQr0q2wm/MV2TKThycIe5e0ytRweW2RZxclogCdQ==}
    engines: {node: '>=8'}
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 5.2.0
      strip-bom: 4.0.0
      type-fest: 0.6.0
    dev: true

  /loader-runner/4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  /loader-utils/0.2.17:
    resolution: {integrity: sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=}
    dependencies:
      big.js: 3.2.0
      emojis-list: 2.1.0
      json5: 0.5.1
      object-assign: 4.1.1
    dev: true

  /loader-utils/1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2
    dev: true

  /loader-utils/2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3
    dev: true

  /locate-path/2.0.0:
    resolution: {integrity: sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=}
    engines: {node: '>=4'}
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0
    dev: true

  /locate-path/3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: true

  /locate-path/5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0

  /locate-path/6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash._reinterpolate/3.0.0:
    resolution: {integrity: sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=}
    dev: true

  /lodash.debounce/4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=}
    dev: true

  /lodash.get/4.4.2:
    resolution: {integrity: sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=}
    dev: true

  /lodash.ismatch/4.4.0:
    resolution: {integrity: sha1-dWy1FQyjum8RCFp4hJZF8Yj4Xzc=}
    dev: true

  /lodash.merge/4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash.template/4.5.0:
    resolution: {integrity: sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A==}
    dependencies:
      lodash._reinterpolate: 3.0.0
      lodash.templatesettings: 4.2.0
    dev: true

  /lodash.templatesettings/4.2.0:
    resolution: {integrity: sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ==}
    dependencies:
      lodash._reinterpolate: 3.0.0
    dev: true

  /lodash.truncate/4.4.2:
    resolution: {integrity: sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=}

  /log-symbols/1.0.2:
    resolution: {integrity: sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      chalk: 1.1.3
    dev: true

  /log-symbols/4.1.0:
    resolution: {integrity: sha1-P727lbRoOsn8eFER55LlWNSr1QM=}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0
    dev: true

  /log-symbols/6.0.0:
    resolution: {integrity: sha512-i24m8rpwhmPIS4zscNzK6MSEhk0DUWa/8iYQWxhffV8jkI4Phvs3F+quL5xvS0gdQR0FyTCMMH33Y78dDTzzIw==}
    engines: {node: '>=18'}
    dependencies:
      chalk: 5.4.1
      is-unicode-supported: 1.3.0
    dev: true

  /log-update/2.3.0:
    resolution: {integrity: sha1-iDKP19HOeTiykoN0bwsbwSayRwg=}
    engines: {node: '>=4'}
    dependencies:
      ansi-escapes: 3.2.0
      cli-cursor: 2.1.0
      wrap-ansi: 3.0.1
    dev: true

  /log-update/4.0.0:
    resolution: {integrity: sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=}
    engines: {node: '>=10'}
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0
    dev: true

  /logform/2.7.0:
    resolution: {integrity: sha512-TFYA4jnP7PVbmlBIfhlSe+WKxs9dklXMTEGcBCIvLhE/Tn3H6Gk1norupVW7m5Cnd4bLcr08AytbyV/xj7f/kQ==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      '@colors/colors': 1.6.0
      '@types/triple-beam': 1.3.5
      fecha: 4.2.3
      ms: 2.1.3
      safe-stable-stringify: 2.5.0
      triple-beam: 1.4.1
    dev: true

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0

  /lower-case/2.0.2:
    resolution: {integrity: sha1-b6I3xj29xKgsoP2ILkci3F5jTig=}
    dependencies:
      tslib: 2.8.1
    dev: true

  /lowercase-keys/2.0.0:
    resolution: {integrity: sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==}
    engines: {node: '>=8'}
    dev: true

  /lru-cache/11.1.0:
    resolution: {integrity: sha512-QIXZUBJUx+2zHUdQujWejBkcD9+cs94tLn0+YL8UrCh+D5sCXZ4c7LaEH48pNwRY3MLDgqUFyhlCyjJPf1WP0A==}
    engines: {node: 20 || >=22}
    dev: true

  /lru-cache/5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1
    dev: true

  /lru-cache/6.0.0:
    resolution: {integrity: sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /make-dir/2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    dev: true

  /make-dir/3.1.0:
    resolution: {integrity: sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.1
    dev: true

  /make-fetch-happen/8.0.14:
    resolution: {integrity: sha1-qrpzrgq1WGrY6qaL2DMyZpOT4iI=}
    engines: {node: '>= 10'}
    dependencies:
      agentkeepalive: 4.6.0
      cacache: 15.3.0
      http-cache-semantics: 4.2.0
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 1.4.1
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      promise-retry: 2.0.1
      socks-proxy-agent: 5.0.1
      ssri: 8.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /make-fetch-happen/9.1.0:
    resolution: {integrity: sha1-UwhaCeeXFDPmdl95cb9j9OBcuWg=}
    engines: {node: '>= 10'}
    dependencies:
      agentkeepalive: 4.6.0
      cacache: 15.3.0
      http-cache-semantics: 4.2.0
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 1.4.1
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.4
      promise-retry: 2.0.1
      socks-proxy-agent: 6.2.1
      ssri: 8.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /make-iterator/1.0.1:
    resolution: {integrity: sha1-KbM/MSqo9UfEpeSQ9Wr87JkTOtY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /map-cache/0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-obj/1.0.1:
    resolution: {integrity: sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-obj/4.3.0:
    resolution: {integrity: sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=}
    engines: {node: '>=8'}
    dev: true

  /markdown-it/12.3.2:
    resolution: {integrity: sha512-TchMembfxfNVpHkbtriWltGWc+m3xszaRD0CZup7GFFhzIgQqxIfn3eGj1yZpfuflzPvfkt611B2Q/Bsk1YnGg==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
      entities: 2.1.0
      linkify-it: 3.0.3
      mdurl: 1.0.1
      uc.micro: 1.0.6
    dev: false

  /markdown-table/2.0.0:
    resolution: {integrity: sha512-Ezda85ToJUBhM6WGaG6veasyym+Tbs3cMAw/ZhOPqXiYsr0jgocBV3j3nx+4lk47plLlIqjwuTm/ywVI+zjJ/A==}
    dependencies:
      repeat-string: 1.6.1
    dev: true

  /math-intrinsics/1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  /mdurl/1.0.1:
    resolution: {integrity: sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4=}
    dev: false

  /media-typer/0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=}
    engines: {node: '>= 0.6'}

  /memfs/3.5.3:
    resolution: {integrity: sha512-UERzLsxzllchadvbPs5aolHh65ISpKpM+ccLbOJ8/vvpBKmAWf+la7dXFy7Mr0ySHbdHrFv5kGFCUHHe6GFEmw==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      fs-monkey: 1.0.6

  /memory-fs/0.5.0:
    resolution: {integrity: sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: true

  /meow/8.1.2:
    resolution: {integrity: sha1-vL5FvaDuFynTUMA8/8g5WjbE6Jc=}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimist': 1.2.5
      camelcase-keys: 6.2.2
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9
    dev: true

  /merge-descriptors/1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}

  /merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  /merge2/1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=}
    engines: {node: '>= 8'}
    dev: true

  /methods/1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=}
    engines: {node: '>= 0.6'}

  /micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  /mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  /mime-db/1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==}
    engines: {node: '>= 0.6'}

  /mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /mime/1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  /mime/2.6.0:
    resolution: {integrity: sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: true

  /mimic-fn/1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==}
    engines: {node: '>=4'}
    dev: true

  /mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  /mimic-fn/3.1.0:
    resolution: {integrity: sha1-ZXVRRbvz42lUuUnBZFBCdFHVynQ=}
    engines: {node: '>=8'}
    dev: true

  /mimic-function/5.0.1:
    resolution: {integrity: sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==}
    engines: {node: '>=18'}
    dev: true

  /mimic-response/1.0.1:
    resolution: {integrity: sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=}
    engines: {node: '>=4'}
    dev: true

  /mimic-response/3.1.0:
    resolution: {integrity: sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=}
    engines: {node: '>=10'}
    dev: true

  /min-indent/1.0.1:
    resolution: {integrity: sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=}
    engines: {node: '>=4'}
    dev: true

  /mini-create-react-context/0.3.3_at7mkepldmzoo6silmqc5bca74:
    resolution: {integrity: sha1-sbK8ZgTTpsXZdSutdpJhVBDrs44=}
    peerDependencies:
      prop-types: ^15.0.0
      react: ^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      prop-types: 15.8.1
      react: 17.0.2
      tiny-warning: 1.0.3
    dev: false

  /minimalistic-assert/1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  /minimatch/10.0.3:
    resolution: {integrity: sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw==}
    engines: {node: 20 || >=22}
    dependencies:
      '@isaacs/brace-expansion': 5.0.0
    dev: true

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.12

  /minimatch/5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 2.0.2
    dev: true

  /minimist-options/4.1.0:
    resolution: {integrity: sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=}
    engines: {node: '>= 6'}
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3
    dev: true

  /minimist/1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}
    dev: true

  /minipass-collect/1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-fetch/1.4.1:
    resolution: {integrity: sha1-114AkdqsGw/9fp1BYp+v99DB8bY=}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13
    dev: true

  /minipass-flush/1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-json-stream/1.0.2:
    resolution: {integrity: sha512-myxeeTm57lYs8pH2nxPzmEEg8DGIgW+9mv6D4JZD2pa81I/OBjeU7PtICXV6c9eRGTA5JMDsuIPUZRCyBMYNhg==}
    dependencies:
      jsonparse: 1.3.1
      minipass: 3.3.6
    dev: true

  /minipass-pipeline/1.2.4:
    resolution: {integrity: sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-sized/1.0.3:
    resolution: {integrity: sha1-cO5afFBSBwr6z7wil36nne81O3A=}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass/2.9.0:
    resolution: {integrity: sha512-wxfUjg9WebH+CUDX/CdbRlh5SmfZiy/hpkxaRI16Y9W56Pa75sWgd/rvFilSgrauD9NyFymP/+JFV3KwzIsJeg==}
    dependencies:
      safe-buffer: 5.2.1
      yallist: 3.1.1
    dev: true

  /minipass/3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /minipass/5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}
    dev: true

  /minipass/7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}
    dev: true

  /minizlib/1.3.3:
    resolution: {integrity: sha512-6ZYMOEnmVsdCeTJVE0W9ZD+pVnE8h9Hma/iOwwRDsdQoePpoX56/8B6z3P9VNwppJuBKNRuFDRNRqRWexT9G9Q==}
    dependencies:
      minipass: 2.9.0
    dev: true

  /minizlib/2.1.2:
    resolution: {integrity: sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    dev: true

  /mississippi/3.0.0:
    resolution: {integrity: sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.5
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.3
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5
    dev: true

  /mkdirp-infer-owner/2.0.0:
    resolution: {integrity: sha1-VdOzaOfYkGXDjzL9OOY48Kth0xY=}
    engines: {node: '>=10'}
    dependencies:
      chownr: 2.0.0
      infer-owner: 1.0.4
      mkdirp: 1.0.4
    dev: true

  /mkdirp/0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /mkdirp/1.0.4:
    resolution: {integrity: sha1-PrXtYmInVteaXw4qIh3+utdcL34=}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /mkdirp/3.0.1:
    resolution: {integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /modify-values/1.0.1:
    resolution: {integrity: sha1-s5OfpgVUZHTj4+PGPWS9Q7TuYCI=}
    engines: {node: '>=0.10.0'}
    dev: true

  /module-alias/2.2.3:
    resolution: {integrity: sha512-23g5BFj4zdQL/b6tor7Ji+QY4pEfNH784BMslY9Qb0UnJWRAt+lQGLYmRaM0KDBwIG23ffEBELhZDP2rhi9f/Q==}
    dev: true

  /moment/2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  /move-concurrently/1.0.1:
    resolution: {integrity: sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=}
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: true

  /ms/2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}

  /ms/2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=}

  /multicast-dns-service-types/1.1.0:
    resolution: {integrity: sha512-cnAsSVxIDsYt0v7HmC0hWZFwwXSh+E6PgCrREDuN/EsjgLwA5XRmlMHhSiDPrt6HxY1gTivEa/Zh7GtODoLevQ==}
    dev: true

  /multicast-dns/6.2.3:
    resolution: {integrity: sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==}
    hasBin: true
    dependencies:
      dns-packet: 1.3.4
      thunky: 1.1.0
    dev: true

  /multicast-dns/7.2.5:
    resolution: {integrity: sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==}
    hasBin: true
    dependencies:
      dns-packet: 5.6.1
      thunky: 1.1.0

  /multimatch/5.0.0:
    resolution: {integrity: sha1-kyuACWPOp6MaAzMo+h4MOhh02+Y=}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimatch': 3.0.5
      array-differ: 3.0.0
      array-union: 2.1.0
      arrify: 2.0.1
      minimatch: 3.1.2
    dev: true

  /mute-stream/0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}
    dev: true

  /mute-stream/1.0.0:
    resolution: {integrity: sha512-avsJQhyd+680gKXyG/sQc0nXaC6rBkPOfyHYcFb9+hdkqQkR9bdnkJ0AMZhke0oesPqIO+mFFJ+IdBc7mst4IA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    dev: true

  /mz-modules/2.1.0:
    resolution: {integrity: sha512-sjk8lcRW3vrVYnZ+W+67L/2rL+jbO5K/N6PFGIcLWTiYytNr22Ah9FDXFs+AQntTM1boZcoHi5qS+CV1seuPog==}
    engines: {node: '>=6.0.0'}
    dependencies:
      glob: 7.2.3
      ko-sleep: 1.1.4
      mkdirp: 0.5.6
      pump: 3.0.3
      rimraf: 2.7.1
    dev: true

  /mz/2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0
    dev: true

  /natural-compare/1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}
    dev: true

  /negotiator/0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  /negotiator/0.6.4:
    resolution: {integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==}
    engines: {node: '>= 0.6'}

  /neo-async/2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=}

  /no-case/3.0.4:
    resolution: {integrity: sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1
    dev: true

  /node-fetch/2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0

  /node-forge/1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}

  /node-gyp/5.1.1:
    resolution: {integrity: sha1-65Ffe2Mck30oLjOu1Ey3oCX2Kj4=}
    engines: {node: '>= 6.0.0'}
    hasBin: true
    dependencies:
      env-paths: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      mkdirp: 0.5.6
      nopt: 4.0.3
      npmlog: 4.1.2
      request: 2.88.2
      rimraf: 2.7.1
      semver: 5.7.2
      tar: 4.4.19
      which: 1.3.1
    dev: true

  /node-gyp/7.1.2:
    resolution: {integrity: sha1-IagQrrsYcSAlHDvOyXmvFYexiK4=}
    engines: {node: '>= 10.12.0'}
    hasBin: true
    dependencies:
      env-paths: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      nopt: 5.0.0
      npmlog: 4.1.2
      request: 2.88.2
      rimraf: 3.0.2
      semver: 7.7.2
      tar: 6.2.1
      which: 2.0.2
    dev: true

  /node-hex/1.0.1:
    resolution: {integrity: sha512-iwpZdvW6Umz12ICmu9IYPRxg0tOLGmU3Tq2tKetejCj3oZd7b2nUXwP3a7QA5M9glWy8wlPS1G3RwM/CdsUbdQ==}
    engines: {node: '>=8.0.0'}
    dev: true

  /node-machine-id/1.1.12:
    resolution: {integrity: sha512-QNABxbrPa3qEIfrE6GOJ7BYIuignnJw7iQ2YPbc3Nla1HzRJjXzZOiikfF8m7eAMfichLt3M4VgLOetqgDmgGQ==}
    dev: true

  /node-plop/0.32.0:
    resolution: {integrity: sha512-lKFSRSRuDHhwDKMUobdsvaWCbbDRbV3jMUSMiajQSQux1aNUevAZVxUHc2JERI//W8ABPRbi3ebYuSuIzkNIpQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      '@types/inquirer': 9.0.8
      change-case: 4.1.2
      del: 7.1.0
      globby: 13.2.2
      handlebars: 4.7.8
      inquirer: 9.3.7
      isbinaryfile: 5.0.4
      lodash.get: 4.4.2
      lower-case: 2.0.2
      mkdirp: 3.0.1
      resolve: 1.22.10
      title-case: 3.0.3
      upper-case: 2.0.2
    dev: true

  /node-releases/2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  /node-version-compare/1.0.3:
    resolution: {integrity: sha512-unO5GpBAh5YqeGULMLpmDT94oanSDMwtZB8KHTKCH/qrGv8bHN0mlDj9xQDAicCYXv2OLnzdi67lidCrcVotVw==}
    dev: true

  /nopt/4.0.3:
    resolution: {integrity: sha1-o3XK2dAv2SEnjZVMIlTVqlfhXkg=}
    hasBin: true
    dependencies:
      abbrev: 1.1.1
      osenv: 0.1.5
    dev: true

  /nopt/5.0.0:
    resolution: {integrity: sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      abbrev: 1.1.1
    dev: true

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.10
      semver: 5.7.2
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-package-data/3.0.3:
    resolution: {integrity: sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=}
    engines: {node: '>=10'}
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.16.1
      semver: 7.7.2
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  /normalize-url/6.1.0:
    resolution: {integrity: sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=}
    engines: {node: '>=10'}
    dev: true

  /npm-bundled/1.1.2:
    resolution: {integrity: sha1-lEx4eJvXOQNbcLqiylzDK42GC8E=}
    dependencies:
      npm-normalize-package-bin: 1.0.1
    dev: true

  /npm-install-checks/4.0.0:
    resolution: {integrity: sha1-o3+sx2Oi/eBJfvLG0Kx8P74A17Q=}
    engines: {node: '>=10'}
    dependencies:
      semver: 7.7.2
    dev: true

  /npm-lifecycle/3.1.5:
    resolution: {integrity: sha1-mILTZCuMgsgVeCoS5qG/7tACYwk=}
    dependencies:
      byline: 5.0.0
      graceful-fs: 4.2.11
      node-gyp: 5.1.1
      resolve-from: 4.0.0
      slide: 1.1.6
      uid-number: 0.0.6
      umask: 1.1.0
      which: 1.3.1
    dev: true

  /npm-normalize-package-bin/1.0.1:
    resolution: {integrity: sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA==}
    dev: true

  /npm-package-arg/8.1.5:
    resolution: {integrity: sha1-M2my1f6P3GdLqn8XhlFN3BVGbkQ=}
    engines: {node: '>=10'}
    dependencies:
      hosted-git-info: 4.1.0
      semver: 7.7.2
      validate-npm-package-name: 3.0.0
    dev: true

  /npm-packlist/2.2.2:
    resolution: {integrity: sha1-B2uXKT+mIPYygzGGp6j2WqphSMg=}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      glob: 7.2.3
      ignore-walk: 3.0.4
      npm-bundled: 1.1.2
      npm-normalize-package-bin: 1.0.1
    dev: true

  /npm-pick-manifest/6.1.1:
    resolution: {integrity: sha1-e1SEyiyQhWX0O38nZE82u4FvUUg=}
    dependencies:
      npm-install-checks: 4.0.0
      npm-normalize-package-bin: 1.0.1
      npm-package-arg: 8.1.5
      semver: 7.7.2
    dev: true

  /npm-registry-fetch/11.0.0:
    resolution: {integrity: sha1-aMG7gQxGVCdg1ipqll+FpwLUOnY=}
    engines: {node: '>=10'}
    dependencies:
      make-fetch-happen: 9.1.0
      minipass: 3.3.6
      minipass-fetch: 1.4.1
      minipass-json-stream: 1.0.2
      minizlib: 2.1.2
      npm-package-arg: 8.1.5
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /npm-registry-fetch/9.0.0:
    resolution: {integrity: sha1-hvP+tM4AMTvAuPH49p2q5vrOFmE=}
    engines: {node: '>=10'}
    dependencies:
      '@npmcli/ci-detect': 1.4.0
      lru-cache: 6.0.0
      make-fetch-happen: 8.0.14
      minipass: 3.3.6
      minipass-fetch: 1.4.1
      minipass-json-stream: 1.0.2
      minizlib: 2.1.2
      npm-package-arg: 8.1.5
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /npm-run-path/4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1

  /npmlog/4.1.2:
    resolution: {integrity: sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==}
    dependencies:
      are-we-there-yet: 1.1.7
      console-control-strings: 1.1.0
      gauge: 2.7.4
      set-blocking: 2.0.0
    dev: true

  /nth-check/2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /number-is-nan/1.0.1:
    resolution: {integrity: sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /oauth-sign/0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}
    dev: true

  /object-assign/4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}

  /object-hash/2.2.0:
    resolution: {integrity: sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw==}
    engines: {node: '>= 6'}
    dev: true

  /object-inspect/1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  /object-is/1.1.6:
    resolution: {integrity: sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
    dev: true

  /object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: true

  /object.assign/4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1
    dev: true

  /object.defaults/1.1.0:
    resolution: {integrity: sha1-On+GgzS0B96gbaFtiNXNKeQ1/s8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-each: 1.0.1
      array-slice: 1.1.0
      for-own: 1.0.0
      isobject: 3.0.1
    dev: true

  /object.entries/1.1.9:
    resolution: {integrity: sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /object.fromentries/2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
    dev: true

  /object.getownpropertydescriptors/2.1.8:
    resolution: {integrity: sha512-qkHIGe4q0lSYMv0XI4SsBTJz3WaURhLvd0lKSgtVuOsJ2krg4SgMw3PIRQFMp07yi++UR3se2mkcLqsBNpBb/A==}
    engines: {node: '>= 0.8'}
    dependencies:
      array.prototype.reduce: 1.0.8
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      gopd: 1.2.0
      safe-array-concat: 1.1.3
    dev: true

  /object.groupby/1.0.3:
    resolution: {integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
    dev: true

  /object.map/1.0.1:
    resolution: {integrity: sha1-z4Plncj8wK1fQlDh94s7gb2AHTc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-own: 1.0.0
      make-iterator: 1.0.1
    dev: true

  /object.pick/1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /object.values/1.2.1:
    resolution: {integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /obuf/1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  /on-finished/2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1

  /on-headers/1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  /once/1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}
    dependencies:
      wrappy: 1.0.2

  /one-time/1.0.0:
    resolution: {integrity: sha512-5DXOiRKwuSEcQ/l0kGCF6Q3jcADFv5tSmRaJck/OqkVFcOzutB134KRSfF0xDrL39MNnqxbHBbUUcjZIhTgb2g==}
    dependencies:
      fn.name: 1.1.0
    dev: true

  /onetime/2.0.1:
    resolution: {integrity: sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=}
    engines: {node: '>=4'}
    dependencies:
      mimic-fn: 1.2.0
    dev: true

  /onetime/5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0

  /onetime/7.0.0:
    resolution: {integrity: sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==}
    engines: {node: '>=18'}
    dependencies:
      mimic-function: 5.0.1
    dev: true

  /only/0.0.2:
    resolution: {integrity: sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==}
    dev: true

  /open/8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  /optionator/0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5
    dev: true

  /ora/5.4.1:
    resolution: {integrity: sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=}
    engines: {node: '>=10'}
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1
    dev: true

  /ora/8.2.0:
    resolution: {integrity: sha512-weP+BZ8MVNnlCm8c0Qdc1WSWq4Qn7I+9CJGm7Qali6g44e/PUzbjNqJX5NJ9ljlNMosfJvg1fKEGILklK9cwnw==}
    engines: {node: '>=18'}
    dependencies:
      chalk: 5.4.1
      cli-cursor: 5.0.0
      cli-spinners: 2.9.2
      is-interactive: 2.0.0
      is-unicode-supported: 2.1.0
      log-symbols: 6.0.0
      stdin-discarder: 0.2.2
      string-width: 7.2.0
      strip-ansi: 7.1.0
    dev: true

  /os-homedir/1.0.2:
    resolution: {integrity: sha1-/7xJiDNuDoM94MFox+8VISGqf7M=}
    engines: {node: '>=0.10.0'}
    dev: true

  /os-name/1.0.3:
    resolution: {integrity: sha1-GzefZINa98Wn9JizV8uVIVwVnt8=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      osx-release: 1.1.0
      win-release: 1.1.1
    dev: true

  /os-tmpdir/1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /osenv/0.1.5:
    resolution: {integrity: sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==}
    dependencies:
      os-homedir: 1.0.2
      os-tmpdir: 1.0.2
    dev: true

  /osx-release/1.1.0:
    resolution: {integrity: sha1-8heRGigTaUmvG/kwiyQeJzfTzWw=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /own-keys/1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0
    dev: true

  /p-cancelable/2.1.1:
    resolution: {integrity: sha1-qrf71BZYL6MqPbSYWcEiSHxe0s8=}
    engines: {node: '>=8'}
    dev: true

  /p-finally/1.0.0:
    resolution: {integrity: sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=}
    engines: {node: '>=4'}
    dev: true

  /p-limit/1.3.0:
    resolution: {integrity: sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==}
    engines: {node: '>=4'}
    dependencies:
      p-try: 1.0.0
    dev: true

  /p-limit/2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0

  /p-limit/3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate/2.0.0:
    resolution: {integrity: sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=}
    engines: {node: '>=4'}
    dependencies:
      p-limit: 1.3.0
    dev: true

  /p-locate/3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-locate/4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0

  /p-locate/5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-map-series/2.1.0:
    resolution: {integrity: sha1-dWDUxFLZ2gwH5pL9v+biyBoqkfI=}
    engines: {node: '>=8'}
    dev: true

  /p-map/2.1.0:
    resolution: {integrity: sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw==}
    engines: {node: '>=6'}
    dev: true

  /p-map/4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-map/5.5.0:
    resolution: {integrity: sha512-VFqfGDHlx87K66yZrNdI4YGtD70IRyd+zSvgks6mzHPRNkoKy+9EKP4SFC77/vTTQYmRmti7dvqC+m5jBrBAcg==}
    engines: {node: '>=12'}
    dependencies:
      aggregate-error: 4.0.1
    dev: true

  /p-pipe/3.1.0:
    resolution: {integrity: sha1-SLV8kiqi4a9qZATLfGvw65zI5g4=}
    engines: {node: '>=8'}
    dev: true

  /p-queue/6.6.2:
    resolution: {integrity: sha1-IGip3PjmfdDsPnory3aBD6qF5CY=}
    engines: {node: '>=8'}
    dependencies:
      eventemitter3: 4.0.7
      p-timeout: 3.2.0
    dev: true

  /p-reduce/2.1.0:
    resolution: {integrity: sha512-2USApvnsutq8uoxZBGbbWM0JIYLiEMJ9RlaN7fAzVNb9OZN0SHjjTTfIcb667XynS5Y1VhwDJVDa72TnPzAYWw==}
    engines: {node: '>=8'}
    dev: true

  /p-retry/4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  /p-timeout/3.2.0:
    resolution: {integrity: sha1-x+F6vJcdKnli74NiazXWNazyPf4=}
    engines: {node: '>=8'}
    dependencies:
      p-finally: 1.0.0
    dev: true

  /p-try/1.0.0:
    resolution: {integrity: sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=}
    engines: {node: '>=4'}
    dev: true

  /p-try/2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  /p-waterfall/2.1.1:
    resolution: {integrity: sha1-YxU6d09HLM3E6ygc2yln/PFYsu4=}
    engines: {node: '>=8'}
    dependencies:
      p-reduce: 2.1.0
    dev: true

  /package-json-from-dist/1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}
    dev: true

  /pacote/11.3.5:
    resolution: {integrity: sha1-c88fw3crUz9XXjnvqWxQvow9ydI=}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@npmcli/git': 2.1.0
      '@npmcli/installed-package-contents': 1.0.7
      '@npmcli/promise-spawn': 1.3.2
      '@npmcli/run-script': 1.8.6
      cacache: 15.3.0
      chownr: 2.0.0
      fs-minipass: 2.1.0
      infer-owner: 1.0.4
      minipass: 3.3.6
      mkdirp: 1.0.4
      npm-package-arg: 8.1.5
      npm-packlist: 2.2.2
      npm-pick-manifest: 6.1.1
      npm-registry-fetch: 11.0.0
      promise-retry: 2.0.1
      read-package-json-fast: 2.0.3
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /parallel-transform/1.2.0:
    resolution: {integrity: sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==}
    dependencies:
      cyclist: 1.0.2
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /param-case/3.0.4:
    resolution: {integrity: sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-filepath/1.0.2:
    resolution: {integrity: sha1-pjISf1Oq89FYdvWHLz/6x2PWyJE=}
    engines: {node: '>=0.8'}
    dependencies:
      is-absolute: 1.0.0
      map-cache: 0.2.2
      path-root: 0.1.1
    dev: true

  /parse-json/4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=}
    engines: {node: '>=4'}
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2
    dev: true

  /parse-json/5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: true

  /parse-node-version/1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}
    dev: true

  /parse-passwd/1.0.0:
    resolution: {integrity: sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /parse-path/4.0.4:
    resolution: {integrity: sha512-Z2lWUis7jlmXC1jeOG9giRO2+FsuyNipeQ43HAjqAZjwSe3SEf+q/84FGPHoso3kyntbxa4c4i77t3m6fGf8cw==}
    dependencies:
      is-ssh: 1.4.1
      protocols: 1.4.8
      qs: 6.14.0
      query-string: 6.14.1
    dev: true

  /parse-url/6.0.5:
    resolution: {integrity: sha512-e35AeLTSIlkw/5GFq70IN7po8fmDUjpDPY1rIK+VubRfsUvBonjQ+PBZG+vWMACnQSmNlvl524IucoDmcioMxA==}
    dependencies:
      is-ssh: 1.4.1
      normalize-url: 6.1.0
      parse-path: 4.0.4
      protocols: 1.4.8
    dev: true

  /parseurl/1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  /pascal-case/3.1.2:
    resolution: {integrity: sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /path-case/3.0.4:
    resolution: {integrity: sha1-kWhkUzTrlCZYN1xW+AtMDLX4LG8=}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /path-dirname/1.0.2:
    resolution: {integrity: sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=}
    dev: true

  /path-exists/3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=}
    engines: {node: '>=4'}
    dev: true

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}

  /path-is-inside/1.0.2:
    resolution: {integrity: sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  /path-parse/1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=}

  /path-root-regex/0.1.2:
    resolution: {integrity: sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-root/0.1.1:
    resolution: {integrity: sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-root-regex: 0.1.2
    dev: true

  /path-scurry/2.0.0:
    resolution: {integrity: sha512-ypGJsmGtdXUOeM5u93TyeIEfEhM6s+ljAhrk5vAvSx8uyY/02OvrZnA0YNGUrPXfpJMgI1ODd3nwz8Npx4O4cg==}
    engines: {node: 20 || >=22}
    dependencies:
      lru-cache: 11.1.0
      minipass: 7.1.2
    dev: true

  /path-to-regexp/0.1.12:
    resolution: {integrity: sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==}

  /path-to-regexp/1.9.0:
    resolution: {integrity: sha512-xIp7/apCFJuUHdDLWe8O1HIkb0kQrOMb/0u6FXQjemHn/ii5LrIzU6bdECnsiTF/GjZkMEKg1xdiZwNqDYlZ6g==}
    dependencies:
      isarray: 0.0.1
    dev: false

  /path-to-regexp/6.3.0:
    resolution: {integrity: sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==}
    dev: true

  /path-type/3.0.0:
    resolution: {integrity: sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==}
    engines: {node: '>=4'}
    dependencies:
      pify: 3.0.0
    dev: true

  /path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: true

  /pause-stream/0.0.11:
    resolution: {integrity: sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=}
    dependencies:
      through: 2.3.8
    dev: true

  /performance-now/2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=}
    dev: true

  /picocolors/0.2.1:
    resolution: {integrity: sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=}
    dev: true

  /picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify/2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=}
    engines: {node: '>=0.10.0'}
    dev: true

  /pify/3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=}
    engines: {node: '>=4'}
    dev: true

  /pify/4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    dev: true

  /pify/5.0.0:
    resolution: {integrity: sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==}
    engines: {node: '>=10'}
    dev: true

  /pinkie-promise/2.0.1:
    resolution: {integrity: sha1-ITXW36ejWMBprJsXh3YogihFD/o=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie: 2.0.4
    dev: true

  /pinkie/2.0.4:
    resolution: {integrity: sha1-clVrgM+g1IqXToDnckjoDtT3+HA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /pkg-dir/3.0.0:
    resolution: {integrity: sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
    dev: true

  /pkg-dir/4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0

  /pkg-up/3.1.0:
    resolution: {integrity: sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 3.0.0
    dev: true

  /platform/1.3.6:
    resolution: {integrity: sha1-SLTOmDFksgnC1FoQetsx9HOm56c=}
    dev: true

  /please-upgrade-node/3.2.0:
    resolution: {integrity: sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=}
    dependencies:
      semver-compare: 1.0.0
    dev: true

  /plop/4.0.1:
    resolution: {integrity: sha512-5n8QU93kvL/ObOzBcPAB1siVFtAH1TZM6TntJ3JK5kXT0jIgnQV+j+uaOWWFJlg1cNkzLYm8klgASF65K36q9w==}
    engines: {node: '>=18'}
    hasBin: true
    dependencies:
      '@types/liftoff': 4.0.3
      chalk: 5.4.1
      interpret: 3.1.1
      liftoff: 4.0.0
      minimist: 1.2.8
      node-plop: 0.32.0
      ora: 8.2.0
      v8flags: 4.0.1
    dev: true

  /pngjs/5.0.0:
    resolution: {integrity: sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==}
    engines: {node: '>=10.13.0'}
    dev: true

  /portfinder/1.0.37:
    resolution: {integrity: sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==}
    engines: {node: '>= 10.12'}
    dependencies:
      async: 3.2.6
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /possible-typed-array-names/1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}
    dev: true

  /postcss-modules-extract-imports/2.0.0:
    resolution: {integrity: sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-modules-local-by-default/3.0.3:
    resolution: {integrity: sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A=}
    engines: {node: '>= 6'}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-modules-scope/2.2.0:
    resolution: {integrity: sha1-OFyuATzHdD9afXYC0Qc6iequYu4=}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-modules-values/3.0.0:
    resolution: {integrity: sha512-1//E5jCBrZ9DmRX+zCtmQtRSV6PV42Ix7Bzj9GbwJceduuf7IqP8MgeTXuRDHOWj2m0VzZD5+roFWDuU8RQjcg==}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
    dev: true

  /postcss-selector-parser/6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: true

  /postcss/7.0.39:
    resolution: {integrity: sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1
    dev: true

  /prelude-ls/1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prettier-linter-helpers/1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      fast-diff: 1.3.0
    dev: true

  /prettier/2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dev: true

  /pretty-error/4.0.0:
    resolution: {integrity: sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=}
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0
    dev: true

  /pretty-time/1.1.0:
    resolution: {integrity: sha1-/7dCmvq7hTXDRqNOQYc63z103Q4=}
    engines: {node: '>=4'}
    dev: true

  /process-nextick-args/2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  /progress/2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}
    dev: true

  /promise-inflight/1.0.1:
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dev: true

  /promise-inflight/1.0.1_bluebird@3.7.2:
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dependencies:
      bluebird: 3.7.2
    dev: true

  /promise-retry/2.0.1:
    resolution: {integrity: sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=}
    engines: {node: '>=10'}
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0
    dev: true

  /promise/7.3.1:
    resolution: {integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==}
    dependencies:
      asap: 2.0.6
    dev: false

  /promzard/0.3.0:
    resolution: {integrity: sha1-JqXW7ox97kyxIggwWs+5O6OCqe4=}
    dependencies:
      read: 1.0.7
    dev: true

  /prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  /proto-list/1.2.4:
    resolution: {integrity: sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk=}
    dev: true

  /protocols/1.4.8:
    resolution: {integrity: sha1-SO6i2PWNlkSkoyyq5dXbKQoHXOg=}
    dev: true

  /protocols/2.0.2:
    resolution: {integrity: sha512-hHVTzba3wboROl0/aWRRG9dMytgH6ow//STBZh43l/wQgmMhYhOFi0EHWAPtoCz9IAUymsyP0TSBHkhgMEGNnQ==}
    dev: true

  /proxy-addr/2.0.7:
    resolution: {integrity: sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  /prr/1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=}
    dev: true

  /psl/1.15.0:
    resolution: {integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==}
    dependencies:
      punycode: 2.3.1
    dev: true

  /pump/2.0.1:
    resolution: {integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==}
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0
    dev: true

  /pump/3.0.3:
    resolution: {integrity: sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==}
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0
    dev: true

  /pumpify/1.5.1:
    resolution: {integrity: sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1
    dev: true

  /punycode/1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=}

  /punycode/2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: true

  /q/1.5.1:
    resolution: {integrity: sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    dev: true

  /qrcode/1.5.4:
    resolution: {integrity: sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      dijkstrajs: 1.0.3
      pngjs: 5.0.0
      yargs: 15.4.1
    dev: true

  /qs/6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0

  /qs/6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0

  /qs/6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}
    dev: true

  /query-string/6.14.1:
    resolution: {integrity: sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=}
    engines: {node: '>=6'}
    dependencies:
      decode-uri-component: 0.2.2
      filter-obj: 1.1.0
      split-on-first: 1.1.0
      strict-uri-encode: 2.0.0
    dev: true

  /queue-microtask/1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=}
    dev: true

  /quick-lru/4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}
    dev: true

  /quick-lru/5.1.1:
    resolution: {integrity: sha1-NmST5rPkKjpoheLpnRj4D7eoyTI=}
    engines: {node: '>=10'}
    dev: true

  /randombytes/2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1

  /range-parser/1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  /raw-body/2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  /react-dom/17.0.2_react@17.0.2:
    resolution: {integrity: sha1-7P+2hF462Nv83EmPDQqTlzZQLCM=}
    peerDependencies:
      react: 17.0.2
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react: 17.0.2
      scheduler: 0.20.2
    dev: false

  /react-is/16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=}

  /react-refresh/0.11.0:
    resolution: {integrity: sha1-dxmLlEcz8PHxqQ55HeRUH58HQEY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /react-router-dom/5.1.2_react@17.0.2:
    resolution: {integrity: sha512-7BPHAaIwWpZS074UKaw1FjVdZBSVWEk8IuDXdB+OkLb8vd/WRQIpA4ag9WQk61aEfQs47wHyjWUoUGGZxpQXew==}
    peerDependencies:
      react: '>=15'
    dependencies:
      '@babel/runtime': 7.27.6
      history: 4.10.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 17.0.2
      react-router: 5.1.2_react@17.0.2
      tiny-invariant: 1.3.3
      tiny-warning: 1.0.3
    dev: false

  /react-router/5.1.2_react@17.0.2:
    resolution: {integrity: sha512-yjEuMFy1ONK246B+rsa0cUam5OeAQ8pyclRDgpxuSCrAlJ1qN9uZ5IgyKC7gQg0w8OM50NXHEegPh/ks9YuR2A==}
    peerDependencies:
      react: '>=15'
    dependencies:
      '@babel/runtime': 7.27.6
      history: 4.10.1
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      mini-create-react-context: 0.3.3_at7mkepldmzoo6silmqc5bca74
      path-to-regexp: 1.9.0
      prop-types: 15.8.1
      react: 17.0.2
      react-is: 16.13.1
      tiny-invariant: 1.3.3
      tiny-warning: 1.0.3
    dev: false

  /react/17.0.2:
    resolution: {integrity: sha1-0LXMUW0p6z7uOD91tihkz7aAADc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
    dev: false

  /read-cmd-shim/2.0.0:
    resolution: {integrity: sha1-SlCnHW8JZTZJOOkDhHb37t45KNk=}
    dev: true

  /read-package-json-fast/2.0.3:
    resolution: {integrity: sha1-MjylKWMNqCyzSzbMC5lmk8mMK4M=}
    engines: {node: '>=10'}
    dependencies:
      json-parse-even-better-errors: 2.3.1
      npm-normalize-package-bin: 1.0.1
    dev: true

  /read-package-json/2.1.2:
    resolution: {integrity: sha1-aZKytmxxdyWf646qxzw6zSi5Iio=}
    dependencies:
      glob: 7.2.3
      json-parse-even-better-errors: 2.3.1
      normalize-package-data: 2.5.0
      npm-normalize-package-bin: 1.0.1
    dev: true

  /read-package-json/3.0.1:
    resolution: {integrity: sha1-xxCPC5OQJXsIwh4wBNJATIBnRLk=}
    engines: {node: '>=10'}
    dependencies:
      glob: 7.2.3
      json-parse-even-better-errors: 2.3.1
      normalize-package-data: 3.0.3
      npm-normalize-package-bin: 1.0.1
    dev: true

  /read-package-json/4.1.2:
    resolution: {integrity: sha512-Dqer4pqzamDE2O4M55xp1qZMuLPqi4ldk2ya648FOMHRjwMzFhuxVrG04wd0c38IsvkVdr3vgHI6z+QTPdAjrQ==}
    engines: {node: '>=10'}
    dependencies:
      glob: 7.2.3
      json-parse-even-better-errors: 2.3.1
      normalize-package-data: 3.0.3
      npm-normalize-package-bin: 1.0.1
    dev: true

  /read-package-tree/5.3.1:
    resolution: {integrity: sha1-oyy2TH8x64pvMe8G+c7fdAaP5jY=}
    dependencies:
      read-package-json: 2.1.2
      readdir-scoped-modules: 1.1.0
      util-promisify: 2.1.0
    dev: true

  /read-pkg-up/3.0.0:
    resolution: {integrity: sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=}
    engines: {node: '>=4'}
    dependencies:
      find-up: 2.1.0
      read-pkg: 3.0.0
    dev: true

  /read-pkg-up/7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1
    dev: true

  /read-pkg/3.0.0:
    resolution: {integrity: sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=}
    engines: {node: '>=4'}
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0
    dev: true

  /read-pkg/5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /read/1.0.7:
    resolution: {integrity: sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=}
    engines: {node: '>=0.8'}
    dependencies:
      mute-stream: 0.0.8
    dev: true

  /readable-stream/2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  /readable-stream/3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  /readdir-glob/1.1.3:
    resolution: {integrity: sha512-v05I2k7xN8zXvPD9N+z/uhXPaj0sUFCe2rcWZIpBsqxfP7xXFQ0tipAd/wjj1YxWyWtUS5IDJpOG82JKt2EAVA==}
    dependencies:
      minimatch: 5.1.6
    dev: true

  /readdir-scoped-modules/1.1.0:
    resolution: {integrity: sha1-jUVAe0+HCg3K68DihnDRjnRRQwk=}
    dependencies:
      debuglog: 1.0.1
      dezalgo: 1.0.4
      graceful-fs: 4.2.11
      once: 1.4.0
    dev: true

  /readdirp/3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /rechoir/0.8.0:
    resolution: {integrity: sha1-Sfhm4NMhRhQto62PDv81KzIV/yI=}
    engines: {node: '>= 10.13.0'}
    dependencies:
      resolve: 1.22.10

  /redent/3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0
    dev: true

  /redis-errors/1.2.0:
    resolution: {integrity: sha512-1qny3OExCf0UvUV/5wpYKf2YwPcOqXzkwKKSmKHiE6ZMQs5heeE/c8eXK+PNllPvmjgAbfnsbpkGZWy8cBpn9w==}
    engines: {node: '>=4'}
    dev: true

  /redis-parser/3.0.0:
    resolution: {integrity: sha512-DJnGAeenTdpMEH6uAJRK/uiyEIH9WVsUmoLwzudwGJUwZPp80PDBWPHXSAGNPwNvIXAbe7MSUB1zQFugFml66A==}
    engines: {node: '>=4'}
    dependencies:
      redis-errors: 1.2.0
    dev: true

  /redis/4.0.0-rc.4:
    resolution: {integrity: sha512-rcYOxvURG1kNg9t+BIY9PLDQh1JH0ZtJJxCQ8FwWSZXJ6TSWmk1VLXWkgzpNj/NAqN0nGdzjVPB4+1lFvOAO3w==}
    dependencies:
      '@node-redis/client': 1.0.6
      '@node-redis/json': 1.0.2_@node-redis+client@1.0.6
      '@node-redis/search': 1.0.5_@node-redis+client@1.0.6
    dev: true

  /reflect-metadata/0.1.14:
    resolution: {integrity: sha512-ZhYeb6nRaXCfhnndflDK8qI6ZQ/YcWZCISRAWICW9XYqMUwjZM9Z0DveWX/ABN01oxSHwVxKQmxeYZSsm0jh5A==}
    dev: false

  /reflect.getprototypeof/1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1
    dev: true

  /regenerate-unicode-properties/10.2.0:
    resolution: {integrity: sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: true

  /regenerate/1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=}
    dev: true

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: false

  /regenerator-runtime/0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  /regexp.prototype.flags/1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2
    dev: true

  /regexpp/3.2.0:
    resolution: {integrity: sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=}
    engines: {node: '>=8'}
    dev: true

  /regexpu-core/6.2.0:
    resolution: {integrity: sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0
    dev: true

  /regjsgen/0.8.0:
    resolution: {integrity: sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==}
    dev: true

  /regjsparser/0.12.0:
    resolution: {integrity: sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==}
    hasBin: true
    dependencies:
      jsesc: 3.0.2
    dev: true

  /relateurl/0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=}
    engines: {node: '>= 0.10'}
    dev: true

  /renderkid/3.0.0:
    resolution: {integrity: sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=}
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1
    dev: true

  /repeat-string/1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=}
    engines: {node: '>=0.10'}
    dev: true

  /request-promise-core/1.1.4_request@2.88.2:
    resolution: {integrity: sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34
    dependencies:
      lodash: 4.17.21
      request: 2.88.2
    dev: true

  /request-promise/4.2.6_request@2.88.2:
    resolution: {integrity: sha512-HCHI3DJJUakkOr8fNoCc73E5nU5bqITjOYFMDrKHYOXWXrgD/SBaC7LjwuPymUprRyuF06UK7hd/lMHkmUXglQ==}
    engines: {node: '>=0.10.0'}
    deprecated: request-promise has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34
    dependencies:
      bluebird: 3.7.2
      request: 2.88.2
      request-promise-core: 1.1.4_request@2.88.2
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0
    dev: true

  /request/2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0
    dev: true

  /require-directory/2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-from-string/2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=}
    engines: {node: '>=0.10.0'}

  /require-main-filename/2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}
    dev: true

  /requires-port/1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=}

  /resolve-alpn/1.2.1:
    resolution: {integrity: sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk=}
    dev: true

  /resolve-cwd/3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}
    dependencies:
      resolve-from: 5.0.0

  /resolve-dir/1.0.1:
    resolution: {integrity: sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=}
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: 2.0.2
      global-modules: 1.0.0
    dev: true

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-from/5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  /resolve-path/1.4.0:
    resolution: {integrity: sha512-i1xevIst/Qa+nA9olDxLWnLk8YZbi8R/7JPbCMcgyWaFR6bKWaexgJgEB5oc2PKMjYdrHynyz0NY+if+H98t1w==}
    engines: {node: '>= 0.8'}
    dependencies:
      http-errors: 1.6.3
      path-is-absolute: 1.0.1
    dev: true

  /resolve-pathname/3.0.0:
    resolution: {integrity: sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng==}
    dev: false

  /resolve/1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /resolve/2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /responselike/2.0.1:
    resolution: {integrity: sha512-4gl03wn3hj1HP3yzgdI7d3lCkF95F21Pz4BPGvKHinyQzALR5CapwC8yIi0Rh58DEMQ/SguC03wFj2k0M/mHhw==}
    dependencies:
      lowercase-keys: 2.0.0
    dev: true

  /restore-cursor/2.0.0:
    resolution: {integrity: sha1-n37ih/gv0ybU/RYpI9YhKe7g368=}
    engines: {node: '>=4'}
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7
    dev: true

  /restore-cursor/3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /restore-cursor/5.1.0:
    resolution: {integrity: sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==}
    engines: {node: '>=18'}
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0
    dev: true

  /retry/0.12.0:
    resolution: {integrity: sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=}
    engines: {node: '>= 4'}
    dev: true

  /retry/0.13.1:
    resolution: {integrity: sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=}
    engines: {node: '>= 4'}

  /reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rfdc/1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}
    dev: true

  /rimraf/2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    dependencies:
      glob: 7.2.3

  /rimraf/6.0.1:
    resolution: {integrity: sha512-9dkvaxAsk/xNXSJzMgFqqMCuFgt2+KsOFek3TMLfo8NCPfWpBmqwyNn5Y+NX56QUYfCtsyhF3ayiboEoUmJk/A==}
    engines: {node: 20 || >=22}
    hasBin: true
    dependencies:
      glob: 11.0.3
      package-json-from-dist: 1.0.1
    dev: true

  /run-async/2.4.1:
    resolution: {integrity: sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=}
    engines: {node: '>=0.12.0'}
    dev: true

  /run-async/3.0.0:
    resolution: {integrity: sha512-540WwVDOMxA6dN6We19EcT9sc3hkXPw5mzRNGM3FkdN/vtE9NFvj5lFAPNwUDmJjXidm3v7TC1cTE7t17Ulm1Q==}
    engines: {node: '>=0.12.0'}
    dev: true

  /run-parallel/1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /run-queue/1.0.3:
    resolution: {integrity: sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=}
    dependencies:
      aproba: 1.2.0
    dev: true

  /rxjs/6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1

  /rxjs/7.8.2:
    resolution: {integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==}
    dependencies:
      tslib: 2.8.1
    dev: true

  /safe-array-concat/1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5
    dev: true

  /safe-buffer/5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  /safe-buffer/5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=}

  /safe-push-apply/1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5
    dev: true

  /safe-regex-test/1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1
    dev: true

  /safe-stable-stringify/2.5.0:
    resolution: {integrity: sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==}
    engines: {node: '>=10'}
    dev: true

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  /sax/1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}
    dev: true

  /scheduler/0.20.2:
    resolution: {integrity: sha1-S67jlDbjSqk7SHS93L8P6Li1DpE=}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
    dev: false

  /schema-utils/1.0.0:
    resolution: {integrity: sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1_ajv@6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/2.7.1:
    resolution: {integrity: sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/3.1.1:
    resolution: {integrity: sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/4.3.2:
    resolution: {integrity: sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1_ajv@8.17.1
      ajv-keywords: 5.1.0_ajv@8.17.1

  /sdk-base/2.0.1:
    resolution: {integrity: sha1-ukAonovfJy7RHdnql+r5jgNtJMY=}
    dependencies:
      get-ready: 1.0.0
    dev: true

  /select-hose/2.0.0:
    resolution: {integrity: sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=}

  /selfsigned/2.4.1:
    resolution: {integrity: sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q==}
    engines: {node: '>=10'}
    dependencies:
      '@types/node-forge': 1.3.13
      node-forge: 1.3.1

  /semver-compare/1.0.0:
    resolution: {integrity: sha1-De4hahyUGrN+nvsXiPavxf9VN/w=}
    dev: true

  /semver/5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true
    dev: true

  /semver/6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true
    dev: true

  /semver/7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /send/0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  /sentence-case/3.0.4:
    resolution: {integrity: sha1-NkWnuMEXx4f96HAgViJbtipFEx8=}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
      upper-case-first: 2.0.2
    dev: true

  /serialize-javascript/4.0.0:
    resolution: {integrity: sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serialize-javascript/5.0.1:
    resolution: {integrity: sha1-eIbshIBJpGJGepfT2Rjrsqr5NPQ=}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serialize-javascript/6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}
    dependencies:
      randombytes: 2.1.0

  /serve-index/1.9.1:
    resolution: {integrity: sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  /serve-static/1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  /set-blocking/2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=}
    dev: true

  /set-function-length/1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
    dev: true

  /set-function-name/2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2
    dev: true

  /set-proto/1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
    dev: true

  /setimmediate/1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}
    dev: false

  /setprototypeof/1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  /setprototypeof/1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  /shallow-clone/3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}
    dependencies:
      kind-of: 6.0.3

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  /shell-exec/1.0.2:
    resolution: {integrity: sha512-jyVd+kU2X+mWKMmGhx4fpWbPsjvD53k9ivqetutVW/BQ+WIZoDoP4d8vUMGezV6saZsiNoW2f9GIhg9Dondohg==}
    dev: true

  /shell-quote/1.8.3:
    resolution: {integrity: sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==}
    engines: {node: '>= 0.4'}

  /side-channel-list/1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  /side-channel-map/1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  /side-channel-weakmap/1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  /side-channel/1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  /signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  /signal-exit/4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}
    dev: true

  /simple-swizzle/0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: true

  /slash/1.0.0:
    resolution: {integrity: sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=}
    engines: {node: '>=0.10.0'}
    dev: true

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /slash/4.0.0:
    resolution: {integrity: sha1-JCI3IXbExsWt214q2oha+YSzlqc=}
    engines: {node: '>=12'}
    dev: true

  /slice-ansi/0.0.4:
    resolution: {integrity: sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=}
    engines: {node: '>=0.10.0'}
    dev: true

  /slice-ansi/3.0.0:
    resolution: {integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-ansi/4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slide/1.1.6:
    resolution: {integrity: sha1-VusCfWW00tzmyy4tMsTUr8nh1wc=}
    dev: true

  /smart-buffer/4.2.0:
    resolution: {integrity: sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}
    dev: true

  /snake-case/3.0.4:
    resolution: {integrity: sha1-Tyu9Vo6ZNavf1ZPzTGkdrbScRSw=}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /sockjs/0.3.24:
    resolution: {integrity: sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==}
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  /socks-proxy-agent/5.0.1:
    resolution: {integrity: sha1-Ay+1gwSKKev/7C5qc/ygdh9IF34=}
    engines: {node: '>= 6'}
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
      socks: 2.8.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /socks-proxy-agent/6.2.1:
    resolution: {integrity: sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==}
    engines: {node: '>= 10'}
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
      socks: 2.8.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /socks/2.8.6:
    resolution: {integrity: sha512-pe4Y2yzru68lXCb38aAqRf5gvN8YdjP1lok5o0J7BOHljkyCGKVz7H3vpVIXKD27rj2giOJ7DwVyk/GWrPHDWA==}
    engines: {node: '>= 10.0.0', npm: '>= 3.0.0'}
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0
    dev: true

  /sort-keys/2.0.0:
    resolution: {integrity: sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg=}
    engines: {node: '>=4'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: true

  /sort-keys/4.2.0:
    resolution: {integrity: sha1-a3Y4zuQsUG//jBzs3nN20hMVvhg=}
    engines: {node: '>=8'}
    dependencies:
      is-plain-obj: 2.1.0
    dev: true

  /source-list-map/2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}
    dev: true

  /source-map-loader/0.2.4:
    resolution: {integrity: sha1-wYsNxuI79m9nkkN1V8VpoR4HInE=}
    engines: {node: '>= 6'}
    dependencies:
      async: 2.6.4
      loader-utils: 1.4.2
    dev: true

  /source-map-support/0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  /source-map/0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}
    dev: true

  /spdx-correct/3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21
    dev: true

  /spdx-exceptions/2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==}
    dev: true

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=}
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21
    dev: true

  /spdx-license-ids/3.0.21:
    resolution: {integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==}
    dev: true

  /spdy-transport/3.0.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==}
    dependencies:
      debug: 4.4.1
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  /spdy/4.0.2:
    resolution: {integrity: sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=}
    engines: {node: '>=6.0.0'}
    dependencies:
      debug: 4.4.1
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  /split-on-first/1.1.0:
    resolution: {integrity: sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=}
    engines: {node: '>=6'}
    dev: true

  /split/1.0.1:
    resolution: {integrity: sha1-YFvZvjA6pZ+zX5Ip++oN3snqB9k=}
    dependencies:
      through: 2.3.8
    dev: true

  /split2/3.2.2:
    resolution: {integrity: sha1-vyzyo32DgxLCSciSBv16F90SNl8=}
    dependencies:
      readable-stream: 3.6.2
    dev: true

  /sprintf-js/1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=}
    dev: true

  /sprintf-js/1.1.3:
    resolution: {integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==}
    dev: true

  /sshpk/1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5
    dev: true

  /ssri/6.0.2:
    resolution: {integrity: sha1-FXk5E08gRk5zAd26PpD/qPdyisU=}
    dependencies:
      figgy-pudding: 3.5.2
    dev: true

  /ssri/8.0.1:
    resolution: {integrity: sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /stack-trace/0.0.10:
    resolution: {integrity: sha512-KGzahc7puUKkzyMt+IqAep+TVNbKP+k2Lmwhub39m1AsTSkaDutx56aDCo+HLDzf/D26BIHTJWNiTG1KAJiQCg==}
    dev: true

  /stackframe/1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}
    dev: true

  /statuses/1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=}
    engines: {node: '>= 0.6'}

  /statuses/2.0.1:
    resolution: {integrity: sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=}
    engines: {node: '>= 0.8'}

  /std-env/3.9.0:
    resolution: {integrity: sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==}
    dev: true

  /stdin-discarder/0.2.2:
    resolution: {integrity: sha512-UhDfHmA92YAlNnCfhmq0VeNL5bDbiZGg7sZ2IvPsXubGkiNa9EC+tUTsjBRsYUAz87btI6/1wf4XoVvQ3uRnmQ==}
    engines: {node: '>=18'}
    dev: true

  /stealthy-require/1.1.1:
    resolution: {integrity: sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /stop-iteration-iterator/1.1.0:
    resolution: {integrity: sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0
    dev: true

  /stream-each/1.2.3:
    resolution: {integrity: sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==}
    dependencies:
      end-of-stream: 1.4.5
      stream-shift: 1.0.3
    dev: true

  /stream-http/2.8.2:
    resolution: {integrity: sha512-QllfrBhqF1DPcz46WxKTs6Mz1Bpc+8Qm6vbqOpVav5odAXwbyzwnEczoWqtxrsmlO+cJqtPrp/8gWKWjaKLLlA==}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2
    dev: true

  /stream-shift/1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}
    dev: true

  /stream-wormhole/1.1.0:
    resolution: {integrity: sha512-gHFfL3px0Kctd6Po0M8TzEvt3De/xu6cnRrjlfYNhwbhLPLwigI2t1nc6jrzNuaYg5C4YF78PPFuQPzRiqn9ew==}
    engines: {node: '>=4.0.0'}
    dev: true

  /streamx/2.22.1:
    resolution: {integrity: sha512-znKXEBxfatz2GBNK02kRnCXjV+AA4kjZIUxeWSr3UGirZMJfTE9uiwKHobnbgxWyL/JWro8tTq+vOqAK1/qbSA==}
    dependencies:
      fast-fifo: 1.3.2
      text-decoder: 1.2.3
    optionalDependencies:
      bare-events: 2.6.0
    dev: true

  /strict-uri-encode/2.0.0:
    resolution: {integrity: sha1-ucczDHBChi9rFC3CdLvMWGbONUY=}
    engines: {node: '>=4'}
    dev: true

  /string-argv/0.3.1:
    resolution: {integrity: sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-width/1.0.2:
    resolution: {integrity: sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=}
    engines: {node: '>=0.10.0'}
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1
    dev: true

  /string-width/2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0
    dev: true

  /string-width/4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string-width/5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: true

  /string-width/7.2.0:
    resolution: {integrity: sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==}
    engines: {node: '>=18'}
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0
    dev: true

  /string.prototype.includes/2.0.1:
    resolution: {integrity: sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
    dev: true

  /string.prototype.matchall/4.0.12:
    resolution: {integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0
    dev: true

  /string.prototype.repeat/1.0.0:
    resolution: {integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==}
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0
    dev: true

  /string.prototype.trim/1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2
    dev: true

  /string.prototype.trimend/1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /string.prototype.trimstart/1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /string_decoder/1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2

  /string_decoder/1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1

  /stringify-object/3.3.0:
    resolution: {integrity: sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw==}
    engines: {node: '>=4'}
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0
    dev: true

  /strip-ansi/3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /strip-ansi/4.0.0:
    resolution: {integrity: sha1-qEeQIusaw2iocTibY1JixQXuNo8=}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1
    dev: true

  /strip-ansi/6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-ansi/7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.1.0
    dev: true

  /strip-bom/3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}
    dev: true

  /strip-bom/4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}
    dev: true

  /strip-final-newline/2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=}
    engines: {node: '>=6'}

  /strip-indent/3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}
    dependencies:
      min-indent: 1.0.1
    dev: true

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=}
    engines: {node: '>=8'}
    dev: true

  /strnum/1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==}
    dev: true

  /strong-log-transformer/2.1.0:
    resolution: {integrity: sha1-D17XjTJeBCGsb5D38Q5pHWrjrhA=}
    engines: {node: '>=4'}
    hasBin: true
    dependencies:
      duplexer: 0.1.2
      minimist: 1.2.8
      through: 2.3.8
    dev: true

  /style-loader/1.3.0_webpack@5.100.1:
    resolution: {integrity: sha1-gotKOzt+eqWEfOe66eh0USEUJJ4=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 2.7.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /supports-color/2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=}
    engines: {node: '>=0.8.0'}
    dev: true

  /supports-color/5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color/7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-color/8.1.1:
    resolution: {integrity: sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /symbol-observable/1.2.0:
    resolution: {integrity: sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /table/6.9.0:
    resolution: {integrity: sha512-9kY+CygyYM6j02t5YFHbNz2FN5QmYGv9zAjVp4lCDjlCw7amdckXlEt/bjMhUIfj4ThGRE4gCUH5+yGnNuPo5A==}
    engines: {node: '>=10.0.0'}
    dependencies:
      ajv: 8.17.1
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /tapable/1.1.3:
    resolution: {integrity: sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==}
    engines: {node: '>=6'}
    dev: true

  /tapable/2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}

  /tar-stream/3.1.7:
    resolution: {integrity: sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==}
    dependencies:
      b4a: 1.6.7
      fast-fifo: 1.3.2
      streamx: 2.22.1
    dev: true

  /tar/4.4.19:
    resolution: {integrity: sha1-Lk1yY98m8rkU3uEMglqxMhI3QvM=}
    engines: {node: '>=4.5'}
    dependencies:
      chownr: 1.1.4
      fs-minipass: 1.2.7
      minipass: 2.9.0
      minizlib: 1.3.3
      mkdirp: 0.5.6
      safe-buffer: 5.2.1
      yallist: 3.1.1
    dev: true

  /tar/6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    dev: true

  /temp-dir/1.0.0:
    resolution: {integrity: sha1-CnwOom06Oa+n4OvqnB/AvE2qAR0=}
    engines: {node: '>=4'}
    dev: true

  /temp-write/4.0.0:
    resolution: {integrity: sha1-zS4IJfyCauctIB3Cbu879+b8kyA=}
    engines: {node: '>=8'}
    dependencies:
      graceful-fs: 4.2.11
      is-stream: 2.0.1
      make-dir: 3.1.0
      temp-dir: 1.0.0
      uuid: 3.4.0
    dev: true

  /terser-webpack-plugin/4.2.3_webpack@5.100.1:
    resolution: {integrity: sha1-KNrvSoO9F8HbApcHCtwH/Iz8apo=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 15.3.0
      find-cache-dir: 3.3.2
      jest-worker: 26.6.2
      p-limit: 3.1.0
      schema-utils: 3.3.0
      serialize-javascript: 5.0.1
      source-map: 0.6.1
      terser: 5.43.1
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - bluebird
    dev: true

  /terser-webpack-plugin/5.3.14_webpack@5.100.1:
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.43.1
      webpack: 5.100.1

  /terser/5.43.1:
    resolution: {integrity: sha512-+6erLbBm0+LROX2sPXlUYx/ux5PyE9K/a92Wrt6oA+WDAoFTdpHE5tCYCI5PNzq2y8df4rA+QgHLJuR4jNymsg==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.10
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21

  /text-decoder/1.2.3:
    resolution: {integrity: sha512-3/o9z3X0X0fTupwsYvR03pJ/DjWuqqrfwBgTQzdWDiQSm9KitAyz/9WqsT2JQW7KV2m+bC2ol/zqpW37NHxLaA==}
    dependencies:
      b4a: 1.6.7
    dev: true

  /text-extensions/1.9.0:
    resolution: {integrity: sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=}
    engines: {node: '>=0.10'}
    dev: true

  /text-hex/1.0.0:
    resolution: {integrity: sha512-uuVGNWzgJ4yhRaNSiubPY7OjISw4sw4E5Uv0wbjp+OzcbmVU/rsT8ujgcXJhn9ypzsgr5vlzpPqP+MBBKcGvbg==}
    dev: true

  /text-table/0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=}
    dev: true

  /thenify-all/1.6.0:
    resolution: {integrity: sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1
    dev: true

  /thenify/3.3.1:
    resolution: {integrity: sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=}
    dependencies:
      any-promise: 1.3.0
    dev: true

  /through/2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}
    dev: true

  /through2/2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2
    dev: true

  /through2/4.0.2:
    resolution: {integrity: sha1-p846wqeosLlmyA58SfBITDsjl2Q=}
    dependencies:
      readable-stream: 3.6.2
    dev: true

  /thunky/1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==}

  /time-stamp/1.1.0:
    resolution: {integrity: sha1-dkpaEa9QVhkhsTPztE5hhofg9cM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /tiny-invariant/1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}
    dev: false

  /tiny-warning/1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}
    dev: false

  /title-case/3.0.3:
    resolution: {integrity: sha1-vGibRvAuQR8dHh0IH3w97KBImYI=}
    dependencies:
      tslib: 2.8.1
    dev: true

  /tmp/0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: true

  /to-arraybuffer/1.0.1:
    resolution: {integrity: sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=}
    dev: true

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=}
    engines: {node: '>=4'}
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /toidentifier/1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=}
    engines: {node: '>=0.6'}

  /tough-cookie/2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1
    dev: true

  /tr46/0.0.3:
    resolution: {integrity: sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=}

  /tr46/2.1.0:
    resolution: {integrity: sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=}
    engines: {node: '>=8'}
    dependencies:
      punycode: 2.3.1
    dev: true

  /trim-newlines/3.0.1:
    resolution: {integrity: sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=}
    engines: {node: '>=8'}
    dev: true

  /triple-beam/1.4.1:
    resolution: {integrity: sha512-aZbgViZrg1QNcG+LULa7nhZpJTZSLm/mXnHXnbAbjmN5aSa0y7V+wvv6+4WaBtpISJzThKy+PIPxc1Nq1EJ9mg==}
    engines: {node: '>= 14.0.0'}
    dev: true

  /ts-loader/6.2.2_typescript@3.9.10:
    resolution: {integrity: sha1-3/o4ebAaGh4KS4XiuEIdwN//HFg=}
    engines: {node: '>=8.6'}
    peerDependencies:
      typescript: '*'
    dependencies:
      chalk: 2.4.2
      enhanced-resolve: 4.5.0
      loader-utils: 1.4.2
      micromatch: 4.0.8
      semver: 6.3.1
      typescript: 3.9.10
    dev: true

  /ts-loader/9.5.2_4iuxj5actolkhzduw7422g664a:
    resolution: {integrity: sha512-Qo4piXvOTWcMGIgRiuFa6nHNm+54HbYaZCKqc9eeZCLRy3XqafQgwX2F7mofrbJG3g7EEb+lkiR+z2Lic2s3Zw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      typescript: '*'
      webpack: ^5.0.0
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.18.2
      micromatch: 4.0.8
      semver: 7.7.2
      source-map: 0.7.4
      typescript: 4.9.5
      webpack: 5.100.1
    dev: true

  /tsconfig-paths/3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0
    dev: true

  /tslib/1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=}

  /tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  /tsscmp/1.0.6:
    resolution: {integrity: sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==}
    engines: {node: '>=0.6.x'}
    dev: true

  /tsutils/3.21.0_typescript@3.9.10:
    resolution: {integrity: sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'
    dependencies:
      tslib: 1.14.1
      typescript: 3.9.10
    dev: true

  /tsutils/3.21.0_typescript@4.9.5:
    resolution: {integrity: sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'
    dependencies:
      tslib: 1.14.1
      typescript: 4.9.5
    dev: true

  /tunnel-agent/0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /turbo-darwin-64/1.13.4:
    resolution: {integrity: sha512-A0eKd73R7CGnRinTiS7txkMElg+R5rKFp9HV7baDiEL4xTG1FIg/56Vm7A5RVgg8UNgG2qNnrfatJtb+dRmNdw==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /turbo-darwin-arm64/1.13.4:
    resolution: {integrity: sha512-eG769Q0NF6/Vyjsr3mKCnkG/eW6dKMBZk6dxWOdrHfrg6QgfkBUk0WUUujzdtVPiUIvsh4l46vQrNVd9EOtbyA==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /turbo-linux-64/1.13.4:
    resolution: {integrity: sha512-Bq0JphDeNw3XEi+Xb/e4xoKhs1DHN7OoLVUbTIQz+gazYjigVZvtwCvgrZI7eW9Xo1eOXM2zw2u1DGLLUfmGkQ==}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /turbo-linux-arm64/1.13.4:
    resolution: {integrity: sha512-BJcXw1DDiHO/okYbaNdcWN6szjXyHWx9d460v6fCHY65G8CyqGU3y2uUTPK89o8lq/b2C8NK0yZD+Vp0f9VoIg==}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /turbo-windows-64/1.13.4:
    resolution: {integrity: sha512-OFFhXHOFLN7A78vD/dlVuuSSVEB3s9ZBj18Tm1hk3aW1HTWTuAw0ReN6ZNlVObZUHvGy8d57OAGGxf2bT3etQw==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /turbo-windows-arm64/1.13.4:
    resolution: {integrity: sha512-u5A+VOKHswJJmJ8o8rcilBfU5U3Y1TTAfP9wX8bFh8teYF1ghP0EhtMRLjhtp6RPa+XCxHHVA2CiC3gbh5eg5g==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /turbo/1.13.4:
    resolution: {integrity: sha512-1q7+9UJABuBAHrcC4Sxp5lOqYS5mvxRrwa33wpIyM18hlOCpRD/fTJNxZ0vhbMcJmz15o9kkVm743mPn7p6jpQ==}
    hasBin: true
    optionalDependencies:
      turbo-darwin-64: 1.13.4
      turbo-darwin-arm64: 1.13.4
      turbo-linux-64: 1.13.4
      turbo-linux-arm64: 1.13.4
      turbo-windows-64: 1.13.4
      turbo-windows-arm64: 1.13.4
    dev: true

  /tweetnacl/0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=}
    dev: true

  /type-check/0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-fest/0.18.1:
    resolution: {integrity: sha1-20vBUaSiz07r+a3V23VQjbbMhB8=}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.4.1:
    resolution: {integrity: sha512-IwzA/LSfD2vC1/YDYMv/zHP4rDF1usCwllsDpbolT3D4fUepIO7f9K70jjmUewU/LmGUKJcwcVtDCpnKk4BPMw==}
    engines: {node: '>=6'}
    dev: true

  /type-fest/0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}
    dev: true

  /type-fest/0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}
    dev: true

  /type-is/1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  /typed-array-buffer/1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15
    dev: true

  /typed-array-byte-length/1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
    dev: true

  /typed-array-byte-offset/1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10
    dev: true

  /typed-array-length/1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10
    dev: true

  /typedarray-to-buffer/3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}
    dependencies:
      is-typedarray: 1.0.0
    dev: true

  /typedarray/0.0.6:
    resolution: {integrity: sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=}
    dev: true

  /typescript/3.9.10:
    resolution: {integrity: sha1-cPORCselHta+952ngAaQsZv3eLg=}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /typescript/4.9.5:
    resolution: {integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /ua-parser-js/1.0.40:
    resolution: {integrity: sha512-z6PJ8Lml+v3ichVojCiB8toQJBuwR42ySM4ezjXIqXK3M0HczmKQ3LF4rhU55PfD99KEEXQG6yb7iOMyvYuHew==}
    hasBin: true
    dev: false

  /uc.micro/1.0.6:
    resolution: {integrity: sha1-nEEagCpAmpH8bPdAgbq6NLJEmaw=}
    dev: false

  /uglify-js/3.17.4:
    resolution: {integrity: sha512-T9q82TJI9e/C1TAxYvfb16xO120tMVFZrGA3f9/P4424DNu6ypK103y0GPFVa17yotwSyZW5iYXgjYHkGrJW/g==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dev: true

  /uglify-js/3.19.3:
    resolution: {integrity: sha512-v3Xu+yuwBXisp6QYTcH4UbH+xYJXqnq2m/LtQVWKWzYc1iehYnLixoQDN9FH6/j9/oybfd6W9Ghwkl8+UMKTKQ==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /uid-number/0.0.6:
    resolution: {integrity: sha1-DqEOgDXo61uOREnwbaHHMGY7qoE=}
    dev: true

  /umask/1.1.0:
    resolution: {integrity: sha1-8pzr8B31F5ErtY/5xOUP3o4zMg0=}
    dev: true

  /unbox-primitive/1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1
    dev: true

  /unc-path-regex/0.1.2:
    resolution: {integrity: sha1-5z3T17DXxe2G+6xrCufYxqadUPo=}
    engines: {node: '>=0.10.0'}
    dev: true

  /undici-types/6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  /unescape/1.0.1:
    resolution: {integrity: sha512-O0+af1Gs50lyH1nUu3ZyYS1cRh01Q/kUKatTOkSs7jukXE6/NebucDVxyiDsA9AQ4JC1V1jUH9EO8JX2nMDgGQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
    dev: true

  /unicode-canonical-property-names-ecmascript/2.0.1:
    resolution: {integrity: sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==}
    engines: {node: '>=4'}
    dev: true

  /unicode-loader/1.0.7:
    resolution: {integrity: sha1-C0GsV5o+zf8BjfjPP7Z4s/9L6aw=}
    dependencies:
      loader-utils: 0.2.17
    dev: true

  /unicode-match-property-ecmascript/2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0
    dev: true

  /unicode-match-property-value-ecmascript/2.2.0:
    resolution: {integrity: sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==}
    engines: {node: '>=4'}
    dev: true

  /unicode-property-aliases-ecmascript/2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}
    dev: true

  /unique-filename/1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}
    dependencies:
      unique-slug: 2.0.2
    dev: true

  /unique-slug/2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}
    dependencies:
      imurmurhash: 0.1.4
    dev: true

  /universal-user-agent/6.0.1:
    resolution: {integrity: sha512-yCzhz6FN2wU1NiiQRogkTQszlQSlpWaw8SvVegAc+bDxbzHgh1vX8uIe8OYyMH6DwH+sdTJsgMl36+mSMdRJIQ==}
    dev: true

  /universalify/2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /unpipe/1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=}
    engines: {node: '>= 0.8'}

  /upath/2.0.1:
    resolution: {integrity: sha1-UMc96mjW9rmQ9R0nnOYIFmXWGos=}
    engines: {node: '>=4'}
    dev: true

  /update-browserslist-db/1.1.3_browserslist@4.25.1:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1

  /upper-case-first/2.0.2:
    resolution: {integrity: sha1-mSwyc/iCq9GdHgKJTMFHEX+EQyQ=}
    dependencies:
      tslib: 2.8.1
    dev: true

  /upper-case/2.0.2:
    resolution: {integrity: sha1-2JgQgj+qsd8VSbfZenb4Ziuub3o=}
    dependencies:
      tslib: 2.8.1
    dev: true

  /uri-js/4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=}
    dependencies:
      punycode: 2.3.1
    dev: true

  /url-loader/3.0.0_67tl4ki5pwptdulvktxqujria4:
    resolution: {integrity: sha512-a84JJbIA5xTFTWyjjcPdnsu+41o/SNE8SpXMdUvXs6Q+LuhCD9E2+0VCiuDWqgo3GGXVlFHzArDmBpj9PgWn4A==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      file-loader: '*'
      webpack: ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      file-loader:
        optional: true
    dependencies:
      file-loader: 5.1.0_webpack@5.100.1
      loader-utils: 1.4.2
      mime: 2.6.0
      schema-utils: 2.7.1
      webpack: 5.100.1_webpack-cli@5.1.4
    dev: true

  /url/0.11.4:
    resolution: {integrity: sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      punycode: 1.4.1
      qs: 6.14.0

  /urllib/2.44.0:
    resolution: {integrity: sha512-zRCJqdfYllRDA9bXUtx+vccyRqtJPKsw85f44zH7zPD28PIvjMqIgw9VwoTLV7xTBWZsbebUFVHU5ghQcWku2A==}
    engines: {node: '>= 0.10.0'}
    peerDependencies:
      proxy-agent: ^5.0.0
    peerDependenciesMeta:
      proxy-agent:
        optional: true
    dependencies:
      any-promise: 1.3.0
      content-type: 1.0.5
      default-user-agent: 1.0.0
      digest-header: 1.1.0
      ee-first: 1.1.1
      formstream: 1.5.1
      humanize-ms: 1.2.1
      iconv-lite: 0.6.3
      pump: 3.0.3
      qs: 6.14.0
      statuses: 1.5.0
      utility: 1.18.0
    dev: true

  /util-deprecate/1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=}

  /util-promisify/2.1.0:
    resolution: {integrity: sha1-PCI2R2xNMsX/PEcAKt18E7moKlM=}
    dependencies:
      object.getownpropertydescriptors: 2.1.8
    dev: true

  /utila/0.4.0:
    resolution: {integrity: sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=}
    dev: true

  /utility/1.18.0:
    resolution: {integrity: sha512-PYxZDA+6QtvRvm//++aGdmKG/cI07jNwbROz0Ql+VzFV1+Z0Dy55NI4zZ7RHc9KKpBePNFwoErqIuqQv/cjiTA==}
    engines: {node: '>= 0.12.0'}
    dependencies:
      copy-to: 2.0.1
      escape-html: 1.0.3
      mkdirp: 0.5.6
      mz: 2.7.0
      unescape: 1.0.1
    dev: true

  /utils-merge/1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=}
    engines: {node: '>= 0.4.0'}

  /uuid/3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    hasBin: true
    dev: true

  /uuid/8.3.2:
    resolution: {integrity: sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=}
    hasBin: true

  /v8-compile-cache/2.4.0:
    resolution: {integrity: sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==}
    dev: true

  /v8flags/4.0.1:
    resolution: {integrity: sha512-fcRLaS4H/hrZk9hYwbdRM35D0U8IYMfEClhXxCivOojl+yTRAZH3Zy2sSy6qVCiGbV9YAtPssP6jaChqC9vPCg==}
    engines: {node: '>= 10.13.0'}
    dev: true

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1
    dev: true

  /validate-npm-package-name/3.0.0:
    resolution: {integrity: sha1-X6kS2B630MdK/BQN5zF/DKffQ34=}
    dependencies:
      builtins: 1.0.3
    dev: true

  /value-equal/1.0.1:
    resolution: {integrity: sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw==}
    dev: false

  /vary/1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=}
    engines: {node: '>= 0.8'}

  /verror/1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0
    dev: true

  /warning/3.0.0:
    resolution: {integrity: sha512-jMBt6pUrKn5I+OGgtQ4YZLdhIeJmObddh6CsibPxyQ5yPZm1XExSyzC1LCNX7BzhxWgiHmizBWJTHJIjMjTQYQ==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /watchpack/2.4.4:
    resolution: {integrity: sha512-c5EGNOiyxxV5qmTtAB7rbiXxi1ooX1pQKMLX/MIabJjRA0SJBQOjKF+KSVfHkr9U1cADPon0mRiVe/riyaiDUA==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  /wbuf/1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==}
    dependencies:
      minimalistic-assert: 1.0.1

  /wcwidth/1.0.1:
    resolution: {integrity: sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=}
    dependencies:
      defaults: 1.0.4
    dev: true

  /webidl-conversions/3.0.1:
    resolution: {integrity: sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=}

  /webidl-conversions/6.1.0:
    resolution: {integrity: sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=}
    engines: {node: '>=10.4'}
    dev: true

  /webpack-cli/5.1.4_p2e7gummt6gr4psp4bot4sod7m:
    resolution: {integrity: sha512-pIDJHIEI9LR0yxHXQ+Qh95k2EvXpWzZ5l+d+jIo+RdSm9MiHfzazIxwwni/p7+x4eJZuvG1AJwgC4TNQ7NRgsg==}
    engines: {node: '>=14.15.0'}
    hasBin: true
    peerDependencies:
      '@webpack-cli/generators': '*'
      webpack: 5.x.x
      webpack-bundle-analyzer: '*'
      webpack-dev-server: '*'
    peerDependenciesMeta:
      '@webpack-cli/generators':
        optional: true
      webpack-bundle-analyzer:
        optional: true
      webpack-dev-server:
        optional: true
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      '@webpack-cli/configtest': 2.1.1_goe534c4oany46n3ybfcwdca2e
      '@webpack-cli/info': 2.0.2_goe534c4oany46n3ybfcwdca2e
      '@webpack-cli/serve': 2.0.5_qyiwwxvrjnka5zdd7c3rrfofzu
      colorette: 2.0.20
      commander: 10.0.1
      cross-spawn: 7.0.6
      envinfo: 7.14.0
      fastest-levenshtein: 1.0.16
      import-local: 3.2.0
      interpret: 3.1.1
      rechoir: 0.8.0
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-dev-server: 4.15.2_goe534c4oany46n3ybfcwdca2e
      webpack-merge: 5.10.0

  /webpack-dev-middleware/5.3.4_webpack@5.100.1:
    resolution: {integrity: sha512-BVdTqhhs+0IfoeAf7EoH5WE+exCmqGerHfDM0IL096Px60Tq2Mn9MAbnaGUe6HiMa41KMCYF19gyzZmBcq/o4Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.3.2
      webpack: 5.100.1_webpack-cli@5.1.4

  /webpack-dev-server/4.15.2_goe534c4oany46n3ybfcwdca2e:
    resolution: {integrity: sha512-0XavAZbNJ5sDrCbkpWL8mia0o5WPOd2YGtxrEiZkBK9FjLppIUK2TgxK6qGD2P3hUXTJNNPVibrerKcx5WkR1g==}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true
    dependencies:
      '@types/bonjour': 3.5.13
      '@types/connect-history-api-fallback': 1.5.4
      '@types/express': 4.17.23
      '@types/serve-index': 1.9.4
      '@types/serve-static': 1.15.8
      '@types/sockjs': 0.3.36
      '@types/ws': 8.18.1
      ansi-html-community: 0.0.8
      bonjour-service: 1.3.0
      chokidar: 3.6.0
      colorette: 2.0.20
      compression: 1.8.0
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.21.2
      graceful-fs: 4.2.11
      html-entities: 2.6.0
      http-proxy-middleware: 2.0.9_@types+express@4.17.23
      ipaddr.js: 2.2.0
      launch-editor: 2.10.0
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.3.2
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack: 5.100.1_webpack-cli@5.1.4
      webpack-cli: 5.1.4_p2e7gummt6gr4psp4bot4sod7m
      webpack-dev-middleware: 5.3.4_webpack@5.100.1
      ws: 8.18.3
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  /webpack-log/2.0.0:
    resolution: {integrity: sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg==}
    engines: {node: '>= 6'}
    dependencies:
      ansi-colors: 3.2.4
      uuid: 3.4.0
    dev: true

  /webpack-merge/5.10.0:
    resolution: {integrity: sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==}
    engines: {node: '>=10.0.0'}
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1

  /webpack-oss/2.1.6_webpack@5.100.1:
    resolution: {integrity: sha512-2KirISg4bg6bvOdKMZNK3i23ZnJR82Pws3warrwcXjaBOk+G9QW6yonCcV8i1m/sZWBt8tBQo6OkjrQFn/KBMg==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      ali-oss: 6.23.0
      ansi-colors: 3.2.4
      fancy-log: 1.3.3
      webpack: 5.100.1_webpack-cli@5.1.4
    transitivePeerDependencies:
      - proxy-agent
      - supports-color
    dev: true

  /webpack-sources/1.4.3:
    resolution: {integrity: sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: true

  /webpack-sources/2.3.1:
    resolution: {integrity: sha1-Vw3grxY5Sf4nIjPCzv4bVvdFEf0=}
    engines: {node: '>=10.13.0'}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: true

  /webpack-sources/3.3.3:
    resolution: {integrity: sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==}
    engines: {node: '>=10.13.0'}

  /webpack/5.100.1:
    resolution: {integrity: sha512-YJB/ESPUe2Locd0NKXmw72Dx8fZQk1gTzI6rc9TAT4+Sypbnhl8jd8RywB1bDsDF9Dy1RUR7gn3q/ZJTd0OZZg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      acorn-import-phases: 1.0.4_acorn@8.15.0
      browserslist: 4.25.1
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.2
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14_webpack@5.100.1
      watchpack: 2.4.4
      webpack-sources: 3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  /webpack/5.100.1_webpack-cli@5.1.4:
    resolution: {integrity: sha512-YJB/ESPUe2Locd0NKXmw72Dx8fZQk1gTzI6rc9TAT4+Sypbnhl8jd8RywB1bDsDF9Dy1RUR7gn3q/ZJTd0OZZg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      acorn-import-phases: 1.0.4_acorn@8.15.0
      browserslist: 4.25.1
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.2
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14_webpack@5.100.1
      watchpack: 2.4.4
      webpack-cli: 5.1.4_p2e7gummt6gr4psp4bot4sod7m
      webpack-sources: 3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  /webpackbar/6.0.1_webpack@5.100.1:
    resolution: {integrity: sha512-TnErZpmuKdwWBdMoexjio3KKX6ZtoKHRVvLIU0A47R0VVBDtx3ZyOJDktgYixhoJokZTYTt1Z37OkO9pnGJa9Q==}
    engines: {node: '>=14.21.3'}
    peerDependencies:
      webpack: 3 || 4 || 5
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      consola: 3.4.2
      figures: 3.2.0
      markdown-table: 2.0.0
      pretty-time: 1.1.0
      std-env: 3.9.0
      webpack: 5.100.1_webpack-cli@5.1.4
      wrap-ansi: 7.0.0
    dev: true

  /websocket-driver/0.7.4:
    resolution: {integrity: sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=}
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: 0.5.10
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  /websocket-extensions/0.1.4:
    resolution: {integrity: sha1-f4RzvIOd/YdgituV1+sHUhFXikI=}
    engines: {node: '>=0.8.0'}

  /whatwg-url/5.0.0:
    resolution: {integrity: sha1-lmRU6HZUYuN2RNNib2dCzotwll0=}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  /whatwg-url/8.7.0:
    resolution: {integrity: sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=}
    engines: {node: '>=10'}
    dependencies:
      lodash: 4.17.21
      tr46: 2.1.0
      webidl-conversions: 6.1.0
    dev: true

  /which-boxed-primitive/1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1
    dev: true

  /which-builtin-type/1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19
    dev: true

  /which-collection/1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4
    dev: true

  /which-module/2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}
    dev: true

  /which-typed-array/1.1.19:
    resolution: {integrity: sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2
    dev: true

  /which/1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /wide-align/1.1.5:
    resolution: {integrity: sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=}
    dependencies:
      string-width: 4.2.3
    dev: true

  /wildcard/2.0.1:
    resolution: {integrity: sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==}

  /win-release/1.1.1:
    resolution: {integrity: sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      semver: 5.7.2
    dev: true

  /winston-daily-rotate-file/4.7.1_winston@3.17.0:
    resolution: {integrity: sha512-7LGPiYGBPNyGHLn9z33i96zx/bd71pjBn9tqQzO3I4Tayv94WPmBNwKC7CO1wPHdP9uvu+Md/1nr6VSH9h0iaA==}
    engines: {node: '>=8'}
    peerDependencies:
      winston: ^3
    dependencies:
      file-stream-rotator: 0.6.1
      object-hash: 2.2.0
      triple-beam: 1.4.1
      winston: 3.17.0
      winston-transport: 4.9.0
    dev: true

  /winston-transport/4.9.0:
    resolution: {integrity: sha512-8drMJ4rkgaPo1Me4zD/3WLfI/zPdA9o2IipKODunnGDcuqbHwjsbB79ylv04LCGGzU0xQ6vTznOMpQGaLhhm6A==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      logform: 2.7.0
      readable-stream: 3.6.2
      triple-beam: 1.4.1
    dev: true

  /winston/3.17.0:
    resolution: {integrity: sha512-DLiFIXYC5fMPxaRg832S6F5mJYvePtmO5G9v9IgUFPhXm9/GkXarH/TUrBAVzhTCzAj9anE/+GjrgXp/54nOgw==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      '@colors/colors': 1.6.0
      '@dabh/diagnostics': 2.0.3
      async: 3.2.6
      is-stream: 2.0.1
      logform: 2.7.0
      one-time: 1.0.0
      readable-stream: 3.6.2
      safe-stable-stringify: 2.5.0
      stack-trace: 0.0.10
      triple-beam: 1.4.1
      winston-transport: 4.9.0
    dev: true

  /word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /wordwrap/1.0.0:
    resolution: {integrity: sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=}
    dev: true

  /wrap-ansi/3.0.1:
    resolution: {integrity: sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=}
    engines: {node: '>=4'}
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0
    dev: true

  /wrap-ansi/6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi/8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: true

  /wrappy/1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}

  /write-file-atomic/2.4.3:
    resolution: {integrity: sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ==}
    dependencies:
      graceful-fs: 4.2.11
      imurmurhash: 0.1.4
      signal-exit: 3.0.7
    dev: true

  /write-file-atomic/3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5
    dev: true

  /write-json-file/3.2.0:
    resolution: {integrity: sha1-Zbvcns2KFFjhWVJ3DMut/P9f5io=}
    engines: {node: '>=6'}
    dependencies:
      detect-indent: 5.0.0
      graceful-fs: 4.2.11
      make-dir: 2.1.0
      pify: 4.0.1
      sort-keys: 2.0.0
      write-file-atomic: 2.4.3
    dev: true

  /write-json-file/4.3.0:
    resolution: {integrity: sha1-kIST1v0jIlNErzJAFuTKj3At0S0=}
    engines: {node: '>=8.3'}
    dependencies:
      detect-indent: 6.1.0
      graceful-fs: 4.2.11
      is-plain-obj: 2.1.0
      make-dir: 3.1.0
      sort-keys: 4.2.0
      write-file-atomic: 3.0.3
    dev: true

  /write-pkg/4.0.0:
    resolution: {integrity: sha1-Z1zATvbBH6rLvHdxskwKu/KiADk=}
    engines: {node: '>=8'}
    dependencies:
      sort-keys: 2.0.0
      type-fest: 0.4.1
      write-json-file: 3.2.0
    dev: true

  /ws/8.18.3:
    resolution: {integrity: sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  /xml2js/0.4.23:
    resolution: {integrity: sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==}
    engines: {node: '>=4.0.0'}
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1
    dev: true

  /xml2js/0.6.2:
    resolution: {integrity: sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1
    dev: true

  /xmlbuilder/11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}
    dev: true

  /xtend/4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: true

  /y18n/4.0.3:
    resolution: {integrity: sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=}
    dev: true

  /y18n/5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=}
    engines: {node: '>=10'}
    dev: true

  /yallist/3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}
    dev: true

  /yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: true

  /yaml/1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}
    dev: true

  /yargs-parser/18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: true

  /yargs-parser/20.2.4:
    resolution: {integrity: sha1-tCiQ8UVmeW+Fro46JSkNIF8VSlQ=}
    engines: {node: '>=10'}
    dev: true

  /yargs-parser/20.2.9:
    resolution: {integrity: sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=}
    engines: {node: '>=10'}
    dev: true

  /yargs-parser/21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}
    dev: true

  /yargs/15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3
    dev: true

  /yargs/16.2.0:
    resolution: {integrity: sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9
    dev: true

  /yargs/17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1
    dev: true

  /ylru/1.4.0:
    resolution: {integrity: sha512-2OQsPNEmBCvXuFlIni/a+Rn+R2pHW9INm0BxXJ4hVDA8TirqMj+J/Rp9ItLatT/5pZqWwefVrTQcHpixsxnVlA==}
    engines: {node: '>= 4.0.0'}
    dev: true

  /yocto-queue/0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=}
    engines: {node: '>=10'}
    dev: true

  /yoctocolors-cjs/2.1.2:
    resolution: {integrity: sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==}
    engines: {node: '>=18'}
    dev: true

  /zip-stream/5.0.2:
    resolution: {integrity: sha512-LfOdrUvPB8ZoXtvOBz6DlNClfvi//b5d56mSWyJi7XbH/HfhOHfUhOqxhT/rUiR7yiktlunqRo+jY6y/cWC/5g==}
    engines: {node: '>= 12.0.0'}
    dependencies:
      archiver-utils: 4.0.1
      compress-commons: 5.0.3
      readable-stream: 3.6.2
    dev: true

  /zscroller/0.4.8:
    resolution: {integrity: sha512-G5NiNLKx2+QhhvZi2yV1jjVXY50otktxkseX2hG2N/eixohOUk0AY8ZpbAxNqS9oJS/NxItCsowupy2tsXxAMw==}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  file:third-party/@hippy/react:
    resolution: {directory: third-party/@hippy/react, type: directory}
    name: '@hippy/react'
    version: 3.3.3
    dependencies:
      '@hippy/react-reconciler': 0.26.4_react@17.0.2
      fast-deep-equal: 2.0.1
      react: 17.0.2
    dev: false

  file:third-party/react-reconciler_react@17.0.2:
    resolution: {directory: third-party/react-reconciler, type: directory}
    id: file:third-party/react-reconciler
    name: react-reconciler
    version: 0.26.4
    engines: {node: '>=0.10.0'}
    peerDependencies:
      react: ^17.0.2
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react: 17.0.2
      scheduler: 0.20.2
    dev: false
